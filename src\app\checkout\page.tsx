'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ShoppingCart, 
  ArrowLeft, 
  CreditCard, 
  Smartphone,
  QrCode,
  MapPin,
  Store,
  CheckCircle,
  Clock
} from 'lucide-react';

interface CartItem {
  id: string;
  qty: number;
  totalPrice: number;
  product: {
    id: string;
    title: string;
    price: number;
    vendor: {
      id: string;
      businessName: string;
      vendorType: string;
    };
    category: {
      name: string;
      type: string;
    };
  };
}

interface Cart {
  id: string;
  items: CartItem[];
  subtotal: number;
}

export default function CheckoutPage() {
  const router = useRouter();
  const [cart, setCart] = useState<Cart | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [orderProcessing, setOrderProcessing] = useState(false);
  const [orderCreated, setOrderCreated] = useState(false);
  const [orderId, setOrderId] = useState('');

  const [formData, setFormData] = useState({
    paymentMethod: '',
    deliveryAddress: '',
    notes: ''
  });

  useEffect(() => {
    fetchCart();
  }, []);

  const fetchCart = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        router.push('/signin');
        return;
      }

      const response = await fetch('/api/cart', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCart(data.cart);
      } else {
        router.push('/cart');
      }
    } catch (error) {
      console.error('Error fetching cart:', error);
      router.push('/cart');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setOrderProcessing(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      
      if (!cart || cart.items.length === 0) {
        setError('Cart is empty');
        return;
      }

      // Check if all items are from the same vendor
      const vendorIds = [...new Set(cart.items.map(item => item.product.vendor.id))];
      if (vendorIds.length > 1) {
        setError('All items must be from the same vendor');
        return;
      }

      const payload = {
        paymentMethod: formData.paymentMethod,
        vendorId: vendorIds[0]
      };

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(payload)
      });

      const data = await response.json();

      if (response.ok) {
        setOrderCreated(true);
        setOrderId(data.order.id);
        
        // Clear cart
        await fetch('/api/cart', {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      } else {
        setError(data.error || 'Failed to create order');
      }
    } catch (error) {
      console.error('Error creating order:', error);
      setError('An error occurred while creating your order');
    } finally {
      setOrderProcessing(false);
    }
  };

  const getCategoryColor = (type: string) => {
    switch (type) {
      case 'FOOD': return 'bg-green-100 text-green-800';
      case 'ECOMMERCE': return 'bg-blue-100 text-blue-800';
      case 'SERVICE': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentIcon = (method: string) => {
    switch (method) {
      case 'CARD': return <CreditCard className="h-5 w-5" />;
      case 'USSD': return <Smartphone className="h-5 w-5" />;
      case 'QR': return <QrCode className="h-5 w-5" />;
      case 'CASH': return <span className="text-lg">$</span>;
      case 'DCASH': return <span className="text-lg">D$</span>;
      default: return <CreditCard className="h-5 w-5" />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!cart || cart.items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">Your cart is empty</p>
            <Button onClick={() => router.push('/cart')}>
              Return to Cart
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (orderCreated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Order Created!</h2>
            <p className="text-gray-600 mb-4">
              Your order has been placed successfully.
            </p>
            <p className="text-sm text-gray-500 mb-6">
              Order ID: #{orderId.slice(-8)}
            </p>
            <div className="space-y-2">
              <Button onClick={() => router.push('/orders')} className="w-full">
                View Order Details
              </Button>
              <Button variant="outline" onClick={() => router.push('/')} className="w-full">
                Continue Shopping
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Button 
              variant="ghost" 
              onClick={() => router.push('/cart')}
              className="mr-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Cart
            </Button>
            <div className="flex items-center">
              <ShoppingCart className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Checkout</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Checkout Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Checkout Information</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Delivery Address */}
                  <div>
                    <Label htmlFor="deliveryAddress">Delivery Address</Label>
                    <Textarea
                      id="deliveryAddress"
                      value={formData.deliveryAddress}
                      onChange={(e) => setFormData(prev => ({ ...prev, deliveryAddress: e.target.value }))}
                      className="mt-1"
                      rows={3}
                      placeholder="Enter your delivery address..."
                      required
                    />
                  </div>

                  {/* Payment Method */}
                  <div>
                    <Label htmlFor="paymentMethod">Payment Method *</Label>
                    <Select value={formData.paymentMethod} onValueChange={(value) => setFormData(prev => ({ ...prev, paymentMethod: value }))}>
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="CARD">
                          <div className="flex items-center gap-2">
                            <CreditCard className="h-4 w-4" />
                            Credit/Debit Card
                          </div>
                        </SelectItem>
                        <SelectItem value="QR">
                          <div className="flex items-center gap-2">
                            <QrCode className="h-4 w-4" />
                            QR Code Payment
                          </div>
                        </SelectItem>
                        <SelectItem value="USSD">
                          <div className="flex items-center gap-2">
                            <Smartphone className="h-4 w-4" />
                            USSD Payment
                          </div>
                        </SelectItem>
                        <SelectItem value="CASH">
                          <div className="flex items-center gap-2">
                            <span className="text-lg">$</span>
                            Cash on Delivery
                          </div>
                        </SelectItem>
                        <SelectItem value="DCASH">
                          <div className="flex items-center gap-2">
                            <span className="text-lg">D$</span>
                            DCash
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Order Notes */}
                  <div>
                    <Label htmlFor="notes">Order Notes (Optional)</Label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      className="mt-1"
                      rows={3}
                      placeholder="Any special instructions for the vendor..."
                    />
                  </div>

                  {/* Terms and Conditions */}
                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      id="terms"
                      required
                      className="mt-1 mr-2"
                    />
                    <label htmlFor="terms" className="text-sm text-gray-600">
                      I agree to the Terms of Service and understand that my order will be processed 
                      according to the vendor's policies.
                    </label>
                  </div>

                  {/* Place Order Button */}
                  <Button
                    type="submit"
                    disabled={orderProcessing || !formData.paymentMethod}
                    className="w-full"
                  >
                    {orderProcessing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Processing Order...
                      </>
                    ) : (
                      <>
                        Place Order
                        <ArrowLeft className="h-4 w-4 ml-2 rotate-180" />
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Vendor Info */}
                  <div className="border-b pb-4">
                    <h4 className="font-medium text-gray-900 mb-2">Vendor</h4>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Store className="h-4 w-4" />
                      {cart.items[0].product.vendor.businessName}
                    </div>
                  </div>

                  {/* Order Items */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Order Items</h4>
                    <div className="space-y-2">
                      {cart.items.map((item) => (
                        <div key={item.id} className="flex justify-between items-start">
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              {item.product.title}
                            </p>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge className={`text-xs ${getCategoryColor(item.product.category.type)}`}>
                                {item.product.category.type}
                              </Badge>
                              <span className="text-xs text-gray-600">
                                Qty: {item.qty}
                              </span>
                            </div>
                          </div>
                          <p className="text-sm font-medium text-gray-900">
                            ${item.totalPrice.toFixed(2)}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Price Breakdown */}
                  <div className="border-t pt-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Subtotal</span>
                        <span>${cart.subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Delivery</span>
                        <span className="text-green-600">Free</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Service Fee</span>
                        <span>$0.00</span>
                      </div>
                      <div className="border-t pt-2">
                        <div className="flex justify-between font-medium">
                          <span>Total</span>
                          <span className="text-lg">${cart.subtotal.toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Payment Method Display */}
                  {formData.paymentMethod && (
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-gray-900 mb-2">Payment Method</h4>
                      <div className="flex items-center gap-2">
                        {getPaymentIcon(formData.paymentMethod)}
                        <span className="text-sm text-gray-600">
                          {formData.paymentMethod === 'CARD' && 'Credit/Debit Card'}
                          {formData.paymentMethod === 'QR' && 'QR Code Payment'}
                          {formData.paymentMethod === 'USSD' && 'USSD Payment'}
                          {formData.paymentMethod === 'CASH' && 'Cash on Delivery'}
                          {formData.paymentMethod === 'DCASH' && 'DCash'}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Estimated Delivery */}
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-gray-900 mb-2">Estimated Delivery</h4>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      2-3 business days
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
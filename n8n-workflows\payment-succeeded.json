{"name": "Payment Succeeded Workflow", "nodes": [{"parameters": {}, "id": "1", "name": "When clicking 'Test workflow'", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"httpMethod": "POST", "path": "payment-succeeded", "options": {}}, "id": "2", "name": "Payment Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [460, 300], "webhookId": "payment-succeeded-webhook"}, {"parameters": {"jsCode": "// Parse webhook data\nconst webhookData = $node[\"Payment Webhook\"].json[\"body\"];\n\n// Extract payment information\nconst paymentInfo = {\n  orderId: webhookData.orderId,\n  transactionId: webhookData.transactionId,\n  paymentMethod: webhookData.paymentMethod,\n  amount: webhookData.amount,\n  currency: webhookData.currency || 'XCD',\n  timestamp: new Date().toISOString(),\n  metadata: webhookData.metadata || {}\n};\n\n// Set up email content for payment confirmation\nconst paymentConfirmationEmail = {\n  to: webhookData.userEmail,\n  subject: `Payment Successful - Order #${webhookData.orderId.slice(-8)}`,\n  html: `\n    <h2>Payment Successful! 🎉</h2>\n    <p>Your payment has been processed successfully. Here are your payment details:</p>\n    \n    <div style=\"border: 1px solid #ddd; padding: 15px; margin: 15px 0; background-color: #f8f9fa;\">\n      <h3>Payment Details</h3>\n      <p><strong>Order ID:</strong> #${webhookData.orderId.slice(-8)}</p>\n      <p><strong>Transaction ID:</strong> ${webhookData.transactionId}</p>\n      <p><strong>Amount:</strong> ${webhookData.currency || 'XCD'} ${webhookData.amount.toFixed(2)}</p>\n      <p><strong>Payment Method:</strong> ${webhookData.paymentMethod}</p>\n      <p><strong>Payment Date:</strong> ${new Date().toLocaleDateString()}</p>\n    </div>\n    \n    <h3>What's Next?</h3>\n    <ul>\n      <li>The vendor will now prepare your order</li>\n      <li>You'll receive notifications when your order status changes</li>\n      <li>Track your order in real-time through your account</li>\n      <li>Contact support if you have any questions</li>\n    </ul>\n    \n    <div style=\"background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 15px 0;\">\n      <p><strong>Order Status:</strong> <span style=\"color: #28a745;\">Paid</span></p>\n      <p>Your order is now being processed by the vendor.</p>\n    </div>\n    \n    <p>Thank you for your business!</p>\n  `,\n  text: `Payment Successful! 🎉\\n\\nYour payment has been processed successfully. Here are your payment details:\\n\\nPayment Details\\nOrder ID: #${webhookData.orderId.slice(-8)}\\nTransaction ID: ${webhookData.transactionId}\\nAmount: ${webhookData.currency || 'XCD'} ${webhookData.amount.toFixed(2)}\\nPayment Method: ${webhookData.paymentMethod}\\nPayment Date: ${new Date().toLocaleDateString()}\\n\\nWhat's Next?\\n- The vendor will now prepare your order\\n- You'll receive notifications when your order status changes\\n- Track your order in real-time through your account\\n- Contact support if you have any questions\\n\\nOrder Status: Paid\\nYour order is now being processed by the vendor.\\n\\nThank you for your business!`\n};\n\n// Set up vendor notification\nconst vendorNotification = {\n  to: webhookData.vendorEmail,\n  subject: `Payment Received - Order #${webhookData.orderId.slice(-8)}`,\n  html: `\n    <h2>Payment Received! 💰</h2>\n    <p>Payment has been successfully processed for the following order:</p>\n    \n    <div style=\"border: 1px solid #ddd; padding: 15px; margin: 15px 0; background-color: #f8f9fa;\">\n      <h3>Payment Details</h3>\n      <p><strong>Order ID:</strong> #${webhookData.orderId.slice(-8)}</p>\n      <p><strong>Transaction ID:</strong> ${webhookData.transactionId}</p>\n      <p><strong>Amount:</strong> ${webhookData.currency || 'XCD'} ${webhookData.amount.toFixed(2)}</p>\n      <p><strong>Payment Method:</strong> ${webhookData.paymentMethod}</p>\n      <p><strong>Customer:</strong> ${webhookData.userName}</p>\n    </div>\n    \n    <h3>Action Required:</h3>\n    <ul>\n      <li>Confirm payment receipt</li>\n      <li>Update order status to \"Paid\"</li>\n      <li>Begin order fulfillment</li>\n      <li>Notify customer of preparation timeline</li>\n    </ul>\n    \n    <p>Please log in to your vendor dashboard to manage this order.</p>\n  `,\n  text: `Payment Received! 💰\\n\\nPayment has been successfully processed for the following order:\\n\\nPayment Details\\nOrder ID: #${webhookData.orderId.slice(-8)}\\nTransaction ID: ${webhookData.transactionId}\\nAmount: ${webhookData.currency || 'XCD'} ${webhookData.amount.toFixed(2)}\\nPayment Method: ${webhookData.paymentMethod}\\nCustomer: ${webhookData.userName}\\n\\nAction Required:\\n- Confirm payment receipt\\n- Update order status to \"Paid\"\\n- Begin order fulfillment\\n- Notify customer of preparation timeline\\n\\nPlease log in to your vendor dashboard to manage this order.`\n};\n\n// Set up SMS notification\nconst smsNotification = {\n  to: webhookData.userPhone,\n  message: `IBT B2B Connect: Payment successful! Order #${webhookData.orderId.slice(-8)} paid. ${webhookData.currency || 'XCD'} ${webhookData.amount.toFixed(2)}. Vendor preparing your order.`\n};\n\n// Prepare order update data\nconst orderUpdate = {\n  orderId: webhookData.orderId,\n  status: 'PAID',\n  paymentStatus: 'COMPLETED',\n  transactionId: webhookData.transactionId,\n  paidAt: new Date().toISOString(),\n  paymentMethod: webhookData.paymentMethod\n};\n\n// Prepare analytics event\nconst analyticsEvent = {\n  eventType: 'PAYMENT_SUCCESS',\n  orderId: webhookData.orderId,\n  userId: webhookData.userId,\n  vendorId: webhookData.vendorId,\n  amount: webhookData.amount,\n  paymentMethod: webhookData.paymentMethod,\n  timestamp: new Date().toISOString()\n};\n\n// Return all data for next nodes\nreturn {\n  paymentInfo,\n  paymentConfirmationEmail,\n  vendorNotification,\n  smsNotification,\n  orderUpdate,\n  analyticsEvent\n};"}, "id": "3", "name": "Process Payment Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"resource": "email", "operation": "send", "fromEmail": "<EMAIL>", "toEmail": "={{ $node[\"Process Payment Data\"].json[\"paymentConfirmationEmail\"][\"to\"] }}", "subject": "={{ $node[\"Process Payment Data\"].json[\"paymentConfirmationEmail\"][\"subject\"] }}", "text": "={{ $node[\"Process Payment Data\"].json[\"paymentConfirmationEmail\"][\"text\"] }}", "html": "={{ $node[\"Process Payment Data\"].json[\"paymentConfirmationEmail\"][\"html\"] }}", "options": {}}, "id": "4", "name": "Email Customer Confirmation", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [900, 150], "credentials": {"sendGridApi": {"id": "1", "name": "SendGrid account"}}}, {"parameters": {"resource": "email", "operation": "send", "fromEmail": "<EMAIL>", "toEmail": "={{ $node[\"Process Payment Data\"].json[\"vendorNotification\"][\"to\"] }}", "subject": "={{ $node[\"Process Payment Data\"].json[\"vendorNotification\"][\"subject\"] }}", "text": "={{ $node[\"Process Payment Data\"].json[\"vendorNotification\"][\"text\"] }}", "html": "={{ $node[\"Process Payment Data\"].json[\"vendorNotification\"][\"html\"] }}", "options": {}}, "id": "5", "name": "Email Vendor Notification", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [900, 300], "credentials": {"sendGridApi": {"id": "1", "name": "SendGrid account"}}}, {"parameters": {"operation": "sendMessage", "from": "={{ process.env.TWILIO_PHONE_NUMBER }}", "to": "={{ $node[\"Process Payment Data\"].json[\"smsNotification\"][\"to\"] }}", "text": "={{ $node[\"Process Payment Data\"].json[\"smsNotification\"][\"message\"] }}", "options": {}}, "id": "6", "name": "SMS Customer Notification", "type": "n8n-nodes-base.twilio", "typeVersion": 2, "position": [900, 450], "credentials": {"twilioApi": {"id": "1", "name": "Twilio account"}}}, {"parameters": {"url": "={{ process.env.API_URL }}/api/orders/{{ $node[\"Process Payment Data\"].json[\"orderUpdate\"][\"orderId\"] }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "status", "value": "={{ $node[\"Process Payment Data\"].json[\"orderUpdate\"][\"status\"] }}"}, {"name": "paymentStatus", "value": "={{ $node[\"Process Payment Data\"].json[\"orderUpdate\"][\"paymentStatus\"] }}"}, {"name": "transactionId", "value": "={{ $node[\"Process Payment Data\"].json[\"orderUpdate\"][\"transactionId\"] }}"}, {"name": "paidAt", "value": "={{ $node[\"Process Payment Data\"].json[\"orderUpdate\"][\"paidAt\"] }}"}, {"name": "paymentMethod", "value": "={{ $node[\"Process Payment Data\"].json[\"orderUpdate\"][\"paymentMethod\"] }}"}]}, "options": {}}, "id": "7", "name": "Update Order Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 600], "credentials": {"httpBasicAuth": {"id": "1", "name": "API Auth"}}}, {"parameters": {"url": "={{ process.env.ANALYTICS_API_URL }}/events", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "eventType", "value": "={{ $node[\"Process Payment Data\"].json[\"analyticsEvent\"][\"eventType\"] }}"}, {"name": "orderId", "value": "={{ $node[\"Process Payment Data\"].json[\"analyticsEvent\"][\"orderId\"] }}"}, {"name": "userId", "value": "={{ $node[\"Process Payment Data\"].json[\"analyticsEvent\"][\"userId\"] }}"}, {"name": "vendorId", "value": "={{ $node[\"Process Payment Data\"].json[\"analyticsEvent\"][\"vendorId\"] }}"}, {"name": "amount", "value": "={{ $node[\"Process Payment Data\"].json[\"analyticsEvent\"][\"amount\"] }}"}, {"name": "paymentMethod", "value": "={{ $node[\"Process Payment Data\"].json[\"analyticsEvent\"][\"paymentMethod\"] }}"}, {"name": "timestamp", "value": "={{ $node[\"Process Payment Data\"].json[\"analyticsEvent\"][\"timestamp\"] }}"}]}, "options": {}}, "id": "8", "name": "Log Analytics Event", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 750], "credentials": {"httpBasicAuth": {"id": "1", "name": "Analytics API"}}}], "pinData": {}, "connections": {"When clicking 'Test workflow'": {"main": [[{"node": "Payment Webhook", "type": "main", "index": 0}]]}, "Payment Webhook": {"main": [[{"node": "Process Payment Data", "type": "main", "index": 0}]]}, "Process Payment Data": {"main": [[{"node": "Email Customer Confirmation", "type": "main", "index": 0}, {"node": "Email Vendor Notification", "type": "main", "index": 0}, {"node": "SMS Customer Notification", "type": "main", "index": 0}, {"node": "Update Order Status", "type": "main", "index": 0}, {"node": "Log Analytics Event", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "payment-succeeded-workflow", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "payment-succeeded", "name": "payment-succeeded"}], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "createdAt": "2024-01-01T00:00:00.000Z"}
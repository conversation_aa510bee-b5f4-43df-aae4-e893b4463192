{"name": "Order Created Workflow", "nodes": [{"parameters": {}, "id": "1", "name": "When clicking 'Test workflow'", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"httpMethod": "POST", "path": "order-created", "options": {}}, "id": "2", "name": "Database Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [460, 300], "webhookId": "order-created-webhook"}, {"parameters": {"jsCode": "// Parse webhook data\nconst webhookData = $node[\"Database Webhook\"].json[\"body\"];\n\n// Extract order information\nconst orderInfo = {\n  orderId: webhookData.orderId,\n  userId: webhookData.userId,\n  vendorId: webhookData.vendorId,\n  total: webhookData.total,\n  status: webhookData.status,\n  paymentMethod: webhookData.paymentMethod,\n  items: webhookData.items || [],\n  timestamp: new Date().toISOString()\n};\n\n// Set up email content for shopper\nconst shopperEmail = {\n  to: webhookData.userEmail,\n  subject: `Order Confirmation - #${webhookData.orderId.slice(-8)}`,\n  html: `\n    <h2>Order Confirmation</h2>\n    <p>Thank you for your order! Here are your order details:</p>\n    \n    <div style=\"border: 1px solid #ddd; padding: 15px; margin: 15px 0;\">\n      <h3>Order #${webhookData.orderId.slice(-8)}</h3>\n      <p><strong>Status:</strong> ${webhookData.status}</p>\n      <p><strong>Total:</strong> $${webhookData.total.toFixed(2)}</p>\n      <p><strong>Payment Method:</strong> ${webhookData.paymentMethod}</p>\n      <p><strong>Order Date:</strong> ${new Date().toLocaleDateString()}</p>\n    </div>\n    \n    <h3>Order Items:</h3>\n    <ul>\n      ${(webhookData.items || []).map(item => \n        `<li>${item.title} - Qty: ${item.qty} - $${(item.qty * item.unitPrice).toFixed(2)}</li>`\n      ).join('')}\n    </ul>\n    \n    <h3>Next Steps:</h3>\n    <ul>\n      <li>The vendor will prepare your order</li>\n      <li>You'll receive updates on your order status</li>\n      <li>Contact support if you have any questions</li>\n    </ul>\n    \n    <p>Thank you for shopping with IBT B2B Connect!</p>\n  `,\n  text: `Order Confirmation\\n\\nThank you for your order! Here are your order details:\\n\\nOrder #${webhookData.orderId.slice(-8)}\\nStatus: ${webhookData.status}\\nTotal: $${webhookData.total.toFixed(2)}\\nPayment Method: ${webhookData.paymentMethod}\\nOrder Date: ${new Date().toLocaleDateString()}\\n\\nOrder Items:\\n${(webhookData.items || []).map(item => `${item.title} - Qty: ${item.qty} - $${(item.qty * item.unitPrice).toFixed(2)}`).join('\\n')}\\n\\nNext Steps:\\n- The vendor will prepare your order\\n- You'll receive updates on your order status\\n- Contact support if you have any questions\\n\\nThank you for shopping with IBT B2B Connect!`\n};\n\n// Set up email content for vendor\nconst vendorEmail = {\n  to: webhookData.vendorEmail,\n  subject: `New Order Received - #${webhookData.orderId.slice(-8)}`,\n  html: `\n    <h2>New Order Received!</h2>\n    <p>You have received a new order. Please review and prepare it for fulfillment.</p>\n    \n    <div style=\"border: 1px solid #ddd; padding: 15px; margin: 15px 0;\">\n      <h3>Order #${webhookData.orderId.slice(-8)}</h3>\n      <p><strong>Customer:</strong> ${webhookData.userName}</p>\n      <p><strong>Email:</strong> ${webhookData.userEmail}</p>\n      <p><strong>Total:</strong> $${webhookData.total.toFixed(2)}</p>\n      <p><strong>Payment Method:</strong> ${webhookData.paymentMethod}</p>\n      <p><strong>Order Time:</strong> ${new Date().toLocaleString()}</p>\n    </div>\n    \n    <h3>Order Items:</h3>\n    <ul>\n      ${(webhookData.items || []).map(item => \n        `<li>${item.title} - Qty: ${item.qty} - $${(item.qty * item.unitPrice).toFixed(2)}</li>`\n      ).join('')}\n    </ul>\n    \n    <h3>Action Required:</h3>\n    <ul>\n      <li>Confirm order details and availability</li>\n      <li>Prepare items for pickup/delivery</li>\n      <li>Update order status when ready</li>\n    </ul>\n    \n    <p>Please log in to your vendor dashboard to manage this order.</p>\n  `,\n  text: `New Order Received!\\n\\nYou have received a new order. Please review and prepare it for fulfillment.\\n\\nOrder #${webhookData.orderId.slice(-8)}\\nCustomer: ${webhookData.userName}\\nEmail: ${webhookData.userEmail}\\nTotal: $${webhookData.total.toFixed(2)}\\nPayment Method: ${webhookData.paymentMethod}\\nOrder Time: ${new Date().toLocaleString()}\\n\\nOrder Items:\\n${(webhookData.items || []).map(item => `${item.title} - Qty: ${item.qty} - $${(item.qty * item.unitPrice).toFixed(2)}`).join('\\n')}\\n\\nAction Required:\\n- Confirm order details and availability\\n- Prepare items for pickup/delivery\\n- Update order status when ready\\n\\nPlease log in to your vendor dashboard to manage this order.`\n};\n\n// Set up SMS content (if phone number available)\nconst smsContent = {\n  to: webhookData.userPhone,\n  message: `IBT B2B Connect: Order #${webhookData.orderId.slice(-8)} confirmed! Total: $${webhookData.total.toFixed(2)}. Check your email for details.`\n};\n\n// Set up vendor webhook payload\nconst vendorWebhookPayload = {\n  orderId: webhookData.orderId,\n  vendorId: webhookData.vendorId,\n  action: 'order_created',\n  timestamp: new Date().toISOString(),\n  data: {\n    total: webhookData.total,\n    itemCount: webhookData.items?.length || 0,\n    customerName: webhookData.userName\n  }\n};\n\n// Update order status in database\nconst orderUpdate = {\n  orderId: webhookData.orderId,\n  status: 'CONFIRMED',\n  confirmationSent: true,\n  confirmationTime: new Date().toISOString()\n};\n\n// Return all data for next nodes\nreturn {\n  orderInfo,\n  shopperEmail,\n  vendorEmail,\n  smsContent,\n  vendorWebhookPayload,\n  orderUpdate\n};"}, "id": "3", "name": "Prepare Order Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"resource": "email", "operation": "send", "fromEmail": "<EMAIL>", "toEmail": "={{ $node[\"Prepare Order Data\"].json[\"shopperEmail\"][\"to\"] }}", "subject": "={{ $node[\"Prepare Order Data\"].json[\"shopperEmail\"][\"subject\"] }}", "text": "={{ $node[\"Prepare Order Data\"].json[\"shopperEmail\"][\"text\"] }}", "html": "={{ $node[\"Prepare Order Data\"].json[\"shopperEmail\"][\"html\"] }}", "options": {}}, "id": "4", "name": "Email to Shopper", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [900, 150], "credentials": {"sendGridApi": {"id": "1", "name": "SendGrid account"}}}, {"parameters": {"resource": "email", "operation": "send", "fromEmail": "<EMAIL>", "toEmail": "={{ $node[\"Prepare Order Data\"].json[\"vendorEmail\"][\"to\"] }}", "subject": "={{ $node[\"Prepare Order Data\"].json[\"vendorEmail\"][\"subject\"] }}", "text": "={{ $node[\"Prepare Order Data\"].json[\"vendorEmail\"][\"text\"] }}", "html": "={{ $node[\"Prepare Order Data\"].json[\"vendorEmail\"][\"html\"] }}", "options": {}}, "id": "5", "name": "Email to Vendor", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [900, 300], "credentials": {"sendGridApi": {"id": "1", "name": "SendGrid account"}}}, {"parameters": {"operation": "sendMessage", "from": "={{ process.env.TWILIO_PHONE_NUMBER }}", "to": "={{ $node[\"Prepare Order Data\"].json[\"smsContent\"][\"to\"] }}", "text": "={{ $node[\"Prepare Order Data\"].json[\"smsContent\"][\"message\"] }}", "options": {}}, "id": "6", "name": "SMS to Shopper", "type": "n8n-nodes-base.twilio", "typeVersion": 2, "position": [900, 450], "credentials": {"twilioApi": {"id": "1", "name": "Twilio account"}}}, {"parameters": {"url": "={{ $node[\"Prepare Order Data\"].json[\"orderInfo\"][\"vendorWebhookUrl\"] }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Vendor-Secret", "value": "={{ process.env.VENDOR_WEBHOOK_SECRET }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "payload", "value": "={{ JSON.stringify($node[\"Prepare Order Data\"].json[\"vendorWebhookPayload\"]) }}"}]}, "options": {"timeout": 30000}}, "id": "7", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 600], "credentials": {"httpHeaderAuth": {"id": "1", "name": "<PERSON><PERSON><PERSON>"}}}, {"parameters": {"url": "={{ process.env.API_URL }}/api/orders/{{ $node[\"Prepare Order Data\"].json[\"orderUpdate\"][\"orderId\"] }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "status", "value": "={{ $node[\"Prepare Order Data\"].json[\"orderUpdate\"][\"status\"] }}"}, {"name": "confirmationSent", "value": "={{ $node[\"Prepare Order Data\"].json[\"orderUpdate\"][\"confirmationSent\"] }}"}, {"name": "confirmationTime", "value": "={{ $node[\"Prepare Order Data\"].json[\"orderUpdate\"][\"confirmationTime\"] }}"}]}, "options": {}}, "id": "8", "name": "Update Order Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 750], "credentials": {"httpBasicAuth": {"id": "1", "name": "API Auth"}}}], "pinData": {}, "connections": {"When clicking 'Test workflow'": {"main": [[{"node": "Database Webhook", "type": "main", "index": 0}]]}, "Database Webhook": {"main": [[{"node": "Prepare Order Data", "type": "main", "index": 0}]]}, "Prepare Order Data": {"main": [[{"node": "Email to Shopper", "type": "main", "index": 0}, {"node": "Email to Vendor", "type": "main", "index": 0}, {"node": "SMS to Shopper", "type": "main", "index": 0}, {"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Update Order Status", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "order-created-workflow", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "order-created", "name": "order-created"}], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "createdAt": "2024-01-01T00:00:00.000Z"}
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Seeding database...');

  // Create sample categories
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { slug: 'food-beverages' },
      update: {},
      create: {
        name: 'Food & Beverages',
        slug: 'food-beverages',
        type: 'FOOD'
      }
    }),
    prisma.category.upsert({
      where: { slug: 'electronics' },
      update: {},
      create: {
        name: 'Electronics',
        slug: 'electronics',
        type: 'ECOMMERCE'
      }
    }),
    prisma.category.upsert({
      where: { slug: 'clothing' },
      update: {},
      create: {
        name: 'Clothing & Accessories',
        slug: 'clothing',
        type: 'ECOMMERCE'
      }
    }),
    prisma.category.upsert({
      where: { slug: 'home-services' },
      update: {},
      create: {
        name: 'Home Services',
        slug: 'home-services',
        type: 'SERVICE'
      }
    }),
    prisma.category.upsert({
      where: { slug: 'professional-services' },
      update: {},
      create: {
        name: 'Professional Services',
        slug: 'professional-services',
        type: 'SERVICE'
      }
    })
  ]);

  // Create USSD menus
  const mainMenu = await prisma.uSSDMenu.upsert({
    where: { code: 'MAIN' },
    update: {},
    create: {
      code: 'MAIN',
      menuJson: JSON.stringify({
        welcome: 'Welcome to IBT B2B Connect Marketplace',
        options: [
          { key: '1', text: 'Login' },
          { key: '2', text: 'Register' },
          { key: '3', text: 'Search Products' },
          { key: '4', text: 'My Orders' },
          { key: '5', text: 'Help' }
        ]
      })
    }
  });

  const loginMenu = await prisma.uSSDMenu.upsert({
    where: { code: 'LOGIN' },
    update: {},
    create: {
      code: 'LOGIN',
      menuJson: JSON.stringify({
        prompt: 'Enter your email address:',
        options: []
      })
    }
  });

  const registerMenu = await prisma.uSSDMenu.upsert({
    where: { code: 'REGISTER' },
    update: {},
    create: {
      code: 'REGISTER',
      menuJson: JSON.stringify({
        prompt: 'Enter your email address:',
        options: []
      })
    }
  });

  const searchMenu = await prisma.uSSDMenu.upsert({
    where: { code: 'SEARCH' },
    update: {},
    create: {
      code: 'SEARCH',
      menuJson: JSON.stringify({
        prompt: 'Enter search term or product name:',
        options: []
      })
    }
  });

  console.log('Database seeded successfully!');
  console.log('Created categories:', categories.length);
  console.log('Created USSD menus: MAIN, LOGIN, REGISTER, SEARCH');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
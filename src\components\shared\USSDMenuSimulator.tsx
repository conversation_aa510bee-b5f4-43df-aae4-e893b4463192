'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Smartphone, 
  MessageSquare, 
  ArrowRight,
  ArrowLeft,
  Phone,
  Send,
  HelpCircle,
  Home,
  User,
  Search,
  ShoppingCart,
  Settings
} from 'lucide-react';

interface USSDMenu {
  id: string;
  code: string;
  menuJson: string;
}

interface USSDSession {
  id: string;
  sessionId: string;
  phoneNumber: string;
  currentMenu: string;
  collectedData: string;
}

interface USSDMenuSimulatorProps {
  phoneNumber?: string;
  initialMenu?: string;
  onSessionUpdate?: (session: USSDSession) => void;
  showInstructions?: boolean;
  className?: string;
}

export default function USSDMenuSimulator({
  phoneNumber = '+1234567890',
  initialMenu = 'MAIN',
  onSessionUpdate,
  showInstructions = true,
  className = ''
}: USSDMenuSimulatorProps) {
  const [session, setSession] = useState<USSDSession | null>(null);
  const [currentMenu, setCurrentMenu] = useState<any>(null);
  const [userInput, setUserInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [history, setHistory] = useState<string[]>([]);
  const [showHelp, setShowHelp] = useState(false);

  const menuDefinitions = {
    MAIN: {
      welcome: 'Welcome to IBT B2B Connect!\n1. Login\n2. Register\n3. Search Products\n4. My Orders\n5. Help',
      options: [
        { key: '1', text: 'Login', icon: User, action: 'LOGIN' },
        { key: '2', text: 'Register', icon: User, action: 'REGISTER' },
        { key: '3', text: 'Search Products', icon: Search, action: 'SEARCH' },
        { key: '4', text: 'My Orders', icon: ShoppingCart, action: 'ORDERS' },
        { key: '5', text: 'Help', icon: HelpCircle, action: 'HELP' }
      ]
    },
    LOGIN: {
      prompt: 'Enter your email address:',
      input: 'email',
      placeholder: '<EMAIL>'
    },
    REGISTER: {
      prompt: 'Enter your email address:',
      input: 'email',
      placeholder: '<EMAIL>'
    },
    SEARCH: {
      prompt: 'Enter search term:',
      input: 'search',
      placeholder: 'What are you looking for?'
    },
    ORDERS: {
      prompt: 'Enter order ID (optional):',
      input: 'orderId',
      placeholder: 'Leave empty to view all orders'
    },
    HELP: {
      welcome: 'Help & Support\n1. FAQ\n2. Contact Support\n3. Terms & Conditions\n4. Back to Main Menu',
      options: [
        { key: '1', text: 'FAQ', icon: HelpCircle, action: 'FAQ' },
        { key: '2', text: 'Contact Support', icon: MessageSquare, action: 'SUPPORT' },
        { key: '3', text: 'Terms & Conditions', icon: Settings, action: 'TERMS' },
        { key: '4', text: 'Back to Main Menu', icon: ArrowLeft, action: 'MAIN' }
      ]
    }
  };

  useEffect(() => {
    initializeSession();
  }, []);

  const initializeSession = async () => {
    setLoading(true);
    try {
      // Simulate API call to start USSD session
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newSession: USSDSession = {
        id: Date.now().toString(),
        sessionId: phoneNumber,
        phoneNumber,
        currentMenu: initialMenu,
        collectedData: '{}'
      };

      setSession(newSession);
      setCurrentMenu(menuDefinitions[initialMenu as keyof typeof menuDefinitions]);
      setHistory([menuDefinitions[initialMenu as keyof typeof menuDefinitions].welcome]);
      
      if (onSessionUpdate) {
        onSessionUpdate(newSession);
      }
    } catch (error) {
      console.error('Error initializing session:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUserInput = async () => {
    if (!userInput.trim() || !session) return;

    setLoading(true);
    const input = userInput.trim();
    setUserInput('');
    
    try {
      // Simulate API call to process USSD response
      await new Promise(resolve => setTimeout(resolve, 800));
      
      let response = '';
      let nextMenu = session.currentMenu;
      const collectedData = JSON.parse(session.collectedData);

      // Handle menu navigation
      const currentMenuDef = menuDefinitions[session.currentMenu as keyof typeof menuDefinitions];
      
      if (currentMenuDef.options) {
        const selectedOption = currentMenuDef.options.find(opt => opt.key === input);
        if (selectedOption) {
          nextMenu = selectedOption.action;
          collectedData[selectedOption.action.toLowerCase()] = input;
        } else {
          response = 'Invalid option. Please try again:';
        }
      } else if (currentMenuDef.input) {
        collectedData[currentMenuDef.input] = input;
        
        // Handle specific input flows
        switch (session.currentMenu) {
          case 'LOGIN':
            if (input === '<EMAIL>') {
              nextMenu = 'LOGIN_PASSWORD';
              response = 'Enter your password:';
            } else {
              response = 'User not found. Please try again:';
              nextMenu = 'LOGIN';
            }
            break;
          case 'LOGIN_PASSWORD':
            if (input === 'password') {
              response = 'Login successful! Welcome back.';
              nextMenu = 'MAIN';
            } else {
              response = 'Invalid password. Try again:';
              nextMenu = 'LOGIN_PASSWORD';
            }
            break;
          case 'REGISTER':
            response = 'Registration initiated! Check your email for next steps.';
            nextMenu = 'MAIN';
            break;
          case 'SEARCH':
            response = `Searching for "${input}"...\nFound 5 results:\n1. Fresh Produce\n2. Electronics\n3. Clothing\n4. Home Services\n5. More options`;
            break;
          case 'ORDERS':
            if (input) {
              response = `Order #${input}:\nStatus: PAID\nTotal: $45.99\nVendor: Fresh Market\nEstimated delivery: 2-3 days`;
            } else {
              response = 'Your Orders:\n1. #12345 - PAID - $45.99\n2. #12346 - PENDING - $23.50\n3. #12347 - FULFILLED - $67.20';
            }
            break;
          default:
            response = 'Processing your request...';
        }
      }

      // Update session
      const updatedSession: USSDSession = {
        ...session,
        currentMenu: nextMenu,
        collectedData: JSON.stringify(collectedData)
      };

      setSession(updatedSession);
      
      // Update menu if changed
      if (nextMenu !== session.currentMenu && menuDefinitions[nextMenu as keyof typeof menuDefinitions]) {
        setCurrentMenu(menuDefinitions[nextMenu as keyof typeof menuDefinitions]);
        if (menuDefinitions[nextMenu as keyof typeof menuDefinitions].welcome) {
          response = menuDefinitions[nextMenu as keyof typeof menuDefinitions].welcome;
        }
      }

      // Add to history
      setHistory(prev => [...prev, response]);
      
      if (onSessionUpdate) {
        onSessionUpdate(updatedSession);
      }
    } catch (error) {
      console.error('Error processing input:', error);
      setHistory(prev => [...prev, 'Error processing request. Please try again.']);
    } finally {
      setLoading(false);
    }
  };

  const resetSession = () => {
    setSession(null);
    setCurrentMenu(null);
    setUserInput('');
    setHistory([]);
    setShowHelp(false);
    initializeSession();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleUserInput();
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Phone Simulator */}
      <Card className="max-w-sm mx-auto">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Smartphone className="h-5 w-5" />
              USSD Simulator
            </div>
            <Badge variant="outline" className="text-xs">
              {session?.sessionId || 'Connecting...'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Phone Screen */}
          <div className="bg-gray-900 text-green-400 rounded-lg p-4 font-mono text-sm min-h-[400px] max-h-[400px] overflow-y-auto">
            {/* Phone Status Bar */}
            <div className="flex justify-between items-center mb-4 text-xs">
              <span>IBT B2B</span>
              <div className="flex items-center gap-1">
                <span>100%</span>
                <span>📶</span>
              </div>
            </div>

            {/* USSD Content */}
            <div className="space-y-2">
              {history.map((message, index) => (
                <div key={index} className="whitespace-pre-line">
                  {message}
                </div>
              ))}
              
              {loading && (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-green-400"></div>
                  <span>Processing...</span>
                </div>
              )}
              
              {currentMenu?.prompt && (
                <div className="text-blue-300">
                  {currentMenu.prompt}
                </div>
              )}
            </div>

            {/* Input Area */}
            <div className="mt-4 flex gap-2">
              <Input
                type="text"
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={currentMenu?.placeholder || 'Enter option...'}
                className="bg-gray-800 text-green-400 border-gray-700"
                disabled={loading}
              />
              <Button
                size="sm"
                onClick={handleUserInput}
                disabled={loading || !userInput.trim()}
                className="bg-green-600 hover:bg-green-700"
              >
                <Send className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Phone Controls */}
          <div className="flex justify-center mt-4 space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setUserInput('')}
              disabled={loading}
            >
              Clear
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={resetSession}
              disabled={loading}
            >
              <Phone className="h-3 w-3 mr-1" />
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      {showInstructions && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <HelpCircle className="h-5 w-5 mr-2" />
              USSD Instructions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">What is USSD?</h4>
                <p className="text-sm text-gray-600">
                  USSD (Unstructured Supplementary Service Data) allows you to access services 
                  directly from your mobile phone without internet connection.
                </p>
              </div>

              <div>
                <h4 className="font-medium mb-2">How to Use:</h4>
                <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                  <li>Dial the USSD code on your phone</li>
                  <li>Follow the menu prompts</li>
                  <li>Enter numbers to select options</li>
                  <li>Provide information when requested</li>
                </ol>
              </div>

              <div>
                <h4 className="font-medium mb-2">Available Features:</h4>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center gap-2 p-2 bg-gray-50 rounded text-sm">
                    <User className="h-4 w-4 text-blue-600" />
                    Account Management
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-gray-50 rounded text-sm">
                    <Search className="h-4 w-4 text-green-600" />
                    Product Search
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-gray-50 rounded text-sm">
                    <ShoppingCart className="h-4 w-4 text-purple-600" />
                    Order Management
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-gray-50 rounded text-sm">
                    <HelpCircle className="h-4 w-4 text-orange-600" />
                    Customer Support
                  </div>
                </div>
              </div>

              <Alert>
                <AlertDescription>
                  <strong>Note:</strong> This is a simulation. In production, you would dial 
                  the actual USSD code on your mobile phone to access these services.
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Session Info */}
      {session && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Session Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Phone Number:</span>
                <span className="font-medium">{session.phoneNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Current Menu:</span>
                <span className="font-medium">{session.currentMenu}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Session ID:</span>
                <span className="font-medium font-mono">{session.sessionId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Data Collected:</span>
                <span className="font-medium">
                  {Object.keys(JSON.parse(session.collectedData)).length} fields
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
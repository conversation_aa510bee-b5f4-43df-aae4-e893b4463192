// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model with authentication and location data
model User {
  id           String   @id @default(cuid())
  email        String   @unique
  name         String?
  passwordHash String
  role         UserRole @default(USER)
  latitude     Float?
  longitude    Float?
  localeId     String?  @unique
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  vendor       Vendor?
  cart         Cart?
  orders       Order[]
  locale       Locale?
  securityLogs SecurityLog[]
  analytics    AnalyticsEvent[]

  @@map("users")
}

// Vendor model for business accounts
model Vendor {
  id           String      @id @default(cuid())
  userId       String      @unique
  businessName String
  vendorType   VendorType
  description  String?
  latitude     Float?
  longitude    Float?
  status       VendorStatus @default(PENDING)
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Relations
  user         User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  products     Product[]
  orders       Order[]
  socialFeeds  SocialFeed[]
  categories   Category[]  @relation("VendorCategories")

  @@map("vendors")
}

// Category model for product classification
model Category {
  id          String       @id @default(cuid())
  name        String
  slug        String       @unique
  type        CategoryType
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relations
  products    Product[]
  vendors     Vendor[]     @relation("VendorCategories")

  @@map("categories")
}

// Product model for marketplace items
model Product {
  id            String   @id @default(cuid())
  vendorId      String
  categoryId    String
  title         String
  description   String?
  price         Float
  images        String?  // JSON string array of image URLs
  latitude      Float?
  longitude     Float?
  stock         Int      @default(0)
  ctcProductId  String?  // Placeholder for CTC integration
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  vendor        Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  category      Category @relation(fields: [categoryId], references: [id])
  cartItems     CartItem[]
  orderItems    OrderItem[]

  @@map("products")
}

// Cart model for user shopping carts
model Cart {
  id             String   @id @default(cuid())
  userId         String   @unique
  locationFilter String?  // JSON string for location filter criteria
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  items          CartItem[]

  @@map("carts")
}

// Cart item model for cart products
model CartItem {
  id        String @id @default(cuid())
  cartId    String
  productId String
  qty       Int    @default(1)
  createdAt DateTime @default(now())

  // Relations
  cart      Cart    @relation(fields: [cartId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id])

  @@unique([cartId, productId])
  @@map("cart_items")
}

// Order model for transactions
model Order {
  id                 String        @id @default(cuid())
  userId             String
  vendorId           String
  total              Float
  status             OrderStatus   @default(PENDING)
  paymentMethod      PaymentMethod
  qrCodeUrl          String?       // QR code image URL
  dcashTransactionId String?       // DCash transaction ID
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt

  // Relations
  user               User          @relation(fields: [userId], references: [id])
  vendor             Vendor        @relation(fields: [vendorId], references: [id])
  items              OrderItem[]

  @@map("orders")
}

// Order item model for ordered products
model OrderItem {
  id         String @id @default(cuid())
  orderId    String
  productId  String
  qty        Int
  unitPrice  Float
  createdAt  DateTime @default(now())

  // Relations
  order      Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product    Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Social feed model for vendor posts
model SocialFeed {
  id          String       @id @default(cuid())
  vendorId    String
  type        SocialFeedType
  content     String
  images      String?      // JSON string array of image URLs
  timestamp   DateTime     @default(now())
  analyticsId String?      // Placeholder for analytics
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relations
  vendor      Vendor       @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  @@map("social_feeds")
}

// USSD session model for mobile interactions
model USSDSession {
  id             String   @id @default(cuid())
  sessionId      String   @unique
  phoneNumber    String
  currentMenu    String?
  collectedData  String?  // JSON string for collected data
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("ussd_sessions")
}

// USSD menu model for menu definitions
model USSDMenu {
  id        String   @id @default(cuid())
  code      String   @unique
  menuJson  String   // JSON string for menu structure
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("ussd_menus")
}

// Locale model for user preferences
model Locale {
  id        String      @id @default(cuid())
  userId    String      @unique
  language  Language?   @default(EN)
  currency  Currency?   @default(XCD)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  // Relations
  user      User        @relation(fields: [userId], references: [id])

  @@map("locales")
}

// Security log model for audit trail
model SecurityLog {
  id         String         @id @default(cuid())
  userId     String
  eventType  SecurityEventType
  status     String?
  timestamp  DateTime       @default(now())

  // Relations
  user       User           @relation(fields: [userId], references: [id])

  @@map("security_logs")
}

// Analytics event model for tracking
model AnalyticsEvent {
  id        String           @id @default(cuid())
  entityId  String
  type      AnalyticsEventType
  data      String?          // JSON string for event data
  timestamp DateTime         @default(now())

  // Relations
  user      User             @relation(fields: [entityId], references: [id])

  @@map("analytics_events")
}

// Enums
enum UserRole {
  USER
  VENDOR
  ADMIN
}

enum VendorType {
  FOOD
  ECOMMERCE
  SERVICE
}

enum VendorStatus {
  PENDING
  APPROVED
  REJECTED
}

enum CategoryType {
  FOOD
  ECOMMERCE
  SERVICE
}

enum OrderStatus {
  PENDING
  PAID
  FULFILLED
  CANCELLED
}

enum PaymentMethod {
  QR
  CARD
  CASH
  USSD
  DCASH
}

enum SocialFeedType {
  OFFER
  NEWS
  UPDATE
  AD
}

enum SecurityEventType {
  LOGIN
  QR
  PAYMENT
}

enum AnalyticsEventType {
  USER_ACTION
  VENDOR_SALE
  SOCIAL_FEED
}

enum Language {
  EN
  ES
  FR
}

enum Currency {
  XCD
  USD
}

import { Request, Response } from 'express';
import { prisma } from '../app';

export const cartController = {
  // Get user's cart
  async getCart(req: Request, res: Response) {
    try {
      const userId = req.user!.userId;

      let cart = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  vendor: {
                    select: {
                      id: true,
                      businessName: true,
                      vendorType: true
                    }
                  },
                  category: true
                }
              }
            }
          }
        }
      });

      // Create cart if it doesn't exist
      if (!cart) {
        cart = await prisma.cart.create({
          data: { userId },
          include: {
            items: {
              include: {
                product: {
                  include: {
                    vendor: {
                      select: {
                        id: true,
                        businessName: true,
                        vendorType: true
                      }
                    },
                    category: true
                  }
                }
              }
            }
          }
        });
      }

      // Calculate totals
      const items = cart.items.map(item => ({
        ...item,
        totalPrice: item.qty * item.product.price
      }));

      const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);

      res.json({
        cart: {
          ...cart,
          items,
          subtotal
        }
      });

    } catch (error) {
      console.error('Get cart error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Add item to cart
  async addToCart(req: Request, res: Response) {
    try {
      const { productId, qty = 1 } = req.body;
      const userId = req.user!.userId;

      if (!productId) {
        return res.status(400).json({ error: 'Product ID is required' });
      }

      // Check if product exists and is in stock
      const product = await prisma.product.findUnique({
        where: { id: productId }
      });

      if (!product) {
        return res.status(404).json({ error: 'Product not found' });
      }

      if (product.stock < qty) {
        return res.status(400).json({ error: 'Insufficient stock' });
      }

      // Get or create cart
      let cart = await prisma.cart.findUnique({
        where: { userId }
      });

      if (!cart) {
        cart = await prisma.cart.create({
          data: { userId }
        });
      }

      // Check if item already exists in cart
      const existingItem = await prisma.cartItem.findUnique({
        where: {
          cartId_productId: {
            cartId: cart.id,
            productId
          }
        }
      });

      if (existingItem) {
        // Update quantity
        const newQty = existingItem.qty + qty;
        if (newQty > product.stock) {
          return res.status(400).json({ error: 'Insufficient stock' });
        }

        await prisma.cartItem.update({
          where: { id: existingItem.id },
          data: { qty: newQty }
        });
      } else {
        // Add new item
        await prisma.cartItem.create({
          data: {
            cartId: cart.id,
            productId,
            qty
          }
        });
      }

      // Get updated cart
      const updatedCart = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  vendor: {
                    select: {
                      id: true,
                      businessName: true,
                      vendorType: true
                    }
                  },
                  category: true
                }
              }
            }
          }
        }
      });

      // Calculate totals
      const items = updatedCart!.items.map(item => ({
        ...item,
        totalPrice: item.qty * item.product.price
      }));

      const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);

      res.json({
        message: 'Item added to cart successfully',
        cart: {
          ...updatedCart!,
          items,
          subtotal
        }
      });

    } catch (error) {
      console.error('Add to cart error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Update cart item quantity
  async updateCart(req: Request, res: Response) {
    try {
      const { productId, qty } = req.body;
      const userId = req.user!.userId;

      if (!productId || qty === undefined) {
        return res.status(400).json({ error: 'Product ID and quantity are required' });
      }

      // Get cart
      const cart = await prisma.cart.findUnique({
        where: { userId }
      });

      if (!cart) {
        return res.status(404).json({ error: 'Cart not found' });
      }

      // Find cart item
      const cartItem = await prisma.cartItem.findUnique({
        where: {
          cartId_productId: {
            cartId: cart.id,
            productId
          }
        }
      });

      if (!cartItem) {
        return res.status(404).json({ error: 'Item not found in cart' });
      }

      // Check stock
      const product = await prisma.product.findUnique({
        where: { id: productId }
      });

      if (!product) {
        return res.status(404).json({ error: 'Product not found' });
      }

      if (product.stock < qty) {
        return res.status(400).json({ error: 'Insufficient stock' });
      }

      if (qty <= 0) {
        // Remove item if quantity is 0 or negative
        await prisma.cartItem.delete({
          where: { id: cartItem.id }
        });
      } else {
        // Update quantity
        await prisma.cartItem.update({
          where: { id: cartItem.id },
          data: { qty }
        });
      }

      // Get updated cart
      const updatedCart = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  vendor: {
                    select: {
                      id: true,
                      businessName: true,
                      vendorType: true
                    }
                  },
                  category: true
                }
              }
            }
          }
        }
      });

      // Calculate totals
      const items = updatedCart!.items.map(item => ({
        ...item,
        totalPrice: item.qty * item.product.price
      }));

      const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);

      res.json({
        message: 'Cart updated successfully',
        cart: {
          ...updatedCart!,
          items,
          subtotal
        }
      });

    } catch (error) {
      console.error('Update cart error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Clear cart
  async clearCart(req: Request, res: Response) {
    try {
      const userId = req.user!.userId;

      // Get cart
      const cart = await prisma.cart.findUnique({
        where: { userId }
      });

      if (!cart) {
        return res.status(404).json({ error: 'Cart not found' });
      }

      // Delete all cart items
      await prisma.cartItem.deleteMany({
        where: { cartId: cart.id }
      });

      res.json({
        message: 'Cart cleared successfully'
      });

    } catch (error) {
      console.error('Clear cart error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};
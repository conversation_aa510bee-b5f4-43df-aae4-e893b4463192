import express from 'express';
import { authenticateToken } from '../middleware/validation';
import { cartController } from '../controllers/cart';

const router = express.Router();

// Protected routes
router.get('/', authenticateToken, cartController.getCart);
router.post('/', authenticateToken, cartController.addToCart);
router.patch('/', authenticateToken, cartController.updateCart);
router.delete('/', authenticateToken, cartController.clearCart);

export default router;
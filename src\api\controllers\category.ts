import { Request, Response } from 'express';
import { prisma } from '../app';

export const categoryController = {
  // Get all categories
  async getAllCategories(req: Request, res: Response) {
    try {
      const { type } = req.query;
      
      const where: any = {};
      
      if (type) {
        where.type = type;
      }

      const categories = await prisma.category.findMany({
        where,
        include: {
          _count: {
            select: {
              products: true
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      });

      res.json(categories);
    } catch (error) {
      console.error('Get categories error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};
import express from 'express';
import { authenticateToken, authorizeRole } from '../middleware/validation';
import { productController } from '../controllers/product';

const router = express.Router();

// Public routes
router.get('/', productController.getAllProducts);
router.get('/:id', productController.getProductById);

// Protected routes
router.post('/', authenticateToken, authorizeR<PERSON>(['VENDOR', 'ADMIN']), productController.createProduct);
router.put('/:id', authenticateToken, authorizeRole(['VENDOR', 'ADMIN']), productController.updateProduct);
router.delete('/:id', authenticateToken, authorizeR<PERSON>(['VENDOR', 'ADMIN']), productController.deleteProduct);

export default router;
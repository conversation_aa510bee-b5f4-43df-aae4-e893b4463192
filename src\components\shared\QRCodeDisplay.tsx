'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  QrCode, 
  Download, 
  Share2, 
  Copy, 
  CheckCircle,
  Clock,
  RefreshCw,
  Smartphone
} from 'lucide-react';
import QRCode from 'qrcode';

interface QRCodeDisplayProps {
  data: string;
  size?: number;
  title?: string;
  description?: string;
  showActions?: boolean;
  onGenerated?: (qrCodeUrl: string) => void;
  status?: 'pending' | 'generated' | 'expired' | 'used';
  expiresAt?: Date;
  amount?: number;
  currency?: string;
}

export default function QRCodeDisplay({
  data,
  size = 300,
  title = 'Payment QR Code',
  description = 'Scan this QR code to complete your payment',
  showActions = true,
  onGenerated,
  status = 'pending',
  expiresAt,
  amount,
  currency = 'XCD'
}: QRCodeDisplayProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [copied, setCopied] = useState(false);
  const [timeLeft, setTimeLeft] = useState<string>('');

  useEffect(() => {
    generateQRCode();
  }, [data, size]);

  useEffect(() => {
    if (expiresAt) {
      const timer = setInterval(() => {
        const now = new Date();
        const diff = expiresAt.getTime() - now.getTime();
        
        if (diff <= 0) {
          setTimeLeft('Expired');
          clearInterval(timer);
        } else {
          const minutes = Math.floor(diff / 60000);
          const seconds = Math.floor((diff % 60000) / 1000);
          setTimeLeft(`${minutes}:${seconds.toString().padStart(2, '0')}`);
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [expiresAt]);

  const generateQRCode = async () => {
    setLoading(true);
    try {
      const url = await QRCode.toDataURL(data, {
        width: size,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M'
      });
      
      setQrCodeUrl(url);
      setLoading(false);
      
      if (onGenerated) {
        onGenerated(url);
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
      setLoading(false);
    }
  };

  const downloadQRCode = () => {
    const link = document.createElement('a');
    link.download = `qrcode-${Date.now()}.png`;
    link.href = qrCodeUrl;
    link.click();
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(data);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
    }
  };

  const shareQRCode = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: description,
          url: data
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback - copy to clipboard
      copyToClipboard();
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'generated': return 'bg-blue-100 text-blue-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'used': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'generated': return <QrCode className="h-4 w-4" />;
      case 'expired': return <RefreshCw className="h-4 w-4" />;
      case 'used': return <CheckCircle className="h-4 w-4" />;
      default: return <QrCode className="h-4 w-4" />;
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center gap-2">
              <QrCode className="h-5 w-5" />
              {title}
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">{description}</p>
          </div>
          <Badge className={`text-xs ${getStatusColor()}`}>
            <div className="flex items-center gap-1">
              {getStatusIcon()}
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </div>
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        {/* Amount Display */}
        {amount && (
          <div className="text-center mb-4">
            <div className="text-3xl font-bold text-gray-900">
              {currency} {amount.toFixed(2)}
            </div>
          </div>
        )}

        {/* QR Code */}
        <div className="flex justify-center mb-6">
          {loading ? (
            <div className="w-[300px] h-[300px] bg-gray-100 rounded-lg flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="relative">
              <img
                src={qrCodeUrl}
                alt="QR Code"
                className="border rounded-lg"
                style={{ width: size, height: size }}
              />
              {status === 'expired' && (
                <div className="absolute inset-0 bg-red-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                  <div className="bg-red-600 text-white px-3 py-1 rounded text-sm font-medium">
                    EXPIRED
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Expiry Timer */}
        {expiresAt && status !== 'expired' && (
          <div className="text-center mb-4">
            <Alert className="border-orange-200 bg-orange-50 text-orange-800">
              <AlertDescription className="flex items-center justify-center gap-2">
                <Clock className="h-4 w-4" />
                Expires in: {timeLeft}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Actions */}
        {showActions && status !== 'expired' && status !== 'used' && (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                onClick={downloadQRCode}
                disabled={loading}
                className="w-full"
              >
                <Download className="h-4 w-4 mr-2" />
                Save
              </Button>
              <Button
                variant="outline"
                onClick={copyToClipboard}
                disabled={loading}
                className="w-full"
              >
                {copied ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </>
                )}
              </Button>
            </div>
            
            <Button
              variant="outline"
              onClick={shareQRCode}
              disabled={loading}
              className="w-full"
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share QR Code
            </Button>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2 flex items-center">
            <Smartphone className="h-4 w-4 mr-2" />
            How to scan:
          </h4>
          <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
            <li>Open your camera or QR code scanner app</li>
            <li>Point your camera at the QR code</li>
            <li>Wait for the code to be recognized</li>
            <li>Follow the prompts to complete payment</li>
          </ol>
        </div>

        {/* Regenerate Button */}
        {status === 'expired' && (
          <Button
            onClick={generateQRCode}
            className="w-full mt-4"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Generate New QR Code
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
{"name": "Social Feed Posted Workflow", "nodes": [{"parameters": {}, "id": "1", "name": "When clicking 'Test workflow'", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"httpMethod": "POST", "path": "social-feed-posted", "options": {}}, "id": "2", "name": "Social Feed Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [460, 300], "webhookId": "social-feed-posted-webhook"}, {"parameters": {"jsCode": "// Parse webhook data\nconst webhookData = $node[\"Social Feed Webhook\"].json[\"body\"];\n\n// Extract social post information\nconst postInfo = {\n  postId: webhookData.postId,\n  vendorId: webhookData.vendorId,\n  type: webhookData.type,\n  content: webhookData.content,\n  images: webhookData.images || [],\n  timestamp: new Date().toISOString(),\n  vendor: {\n    businessName: webhookData.businessName,\n    vendorType: webhookData.vendorType\n  }\n};\n\n// Set up analytics data\nconst analyticsData = {\n  postId: webhookData.postId,\n  vendorId: webhookData.vendorId,\n  type: 'SOCIAL_FEED_POST',\n  contentType: webhookData.type,\n  hasImages: webhookData.images && webhookData.images.length > 0,\n  contentLength: webhookData.content.length,\n  timestamp: new Date().toISOString()\n};\n\n// Set up follower notifications\nconst followerNotifications = {\n  vendorId: webhookData.vendorId,\n  postId: webhookData.postId,\n  type: webhookData.type,\n  preview: webhookData.content.substring(0, 100) + (webhookData.content.length > 100 ? '...' : ''),\n  businessName: webhookData.businessName,\n  timestamp: new Date().toISOString()\n};\n\n// Set up social media sharing (if applicable)\nconst socialShare = {\n  title: `${webhookData.businessName} - ${webhookData.type}`,\n  content: webhookData.content,\n  images: webhookData.images || [],\n  url: `${process.env.FRONTEND_URL}/social/${webhookData.postId}`,\n  hashtags: [\n    'IBTB2BConnect',\n    'LocalBusiness',\n    webhookData.vendorType.toLowerCase(),\n    webhookData.type.toLowerCase()\n  ]\n};\n\n// Prepare trending detection data\nconst trendingData = {\n  vendorId: webhookData.vendorId,\n  businessName: webhookData.businessName,\n  postType: webhookData.type,\n  engagement: {\n    initial: 0,\n    projected: calculateProjectedEngagement(webhookData)\n  },\n  keywords: extractKeywords(webhookData.content),\n  timestamp: new Date().toISOString()\n};\n\n// Helper functions\nfunction calculateProjectedEngagement(post) {\n  // Simple engagement projection based on content type and length\n  let baseScore = 10;\n  \n  // Boost for images\n  if (post.images && post.images.length > 0) {\n    baseScore += post.images.length * 15;\n  }\n  \n  // Boost for content length (optimal range)\n  if (post.content.length >= 50 && post.content.length <= 200) {\n    baseScore += 10;\n  }\n  \n  // Type-specific multipliers\n  const typeMultipliers = {\n    'OFFER': 2.5,\n    'NEWS': 1.8,\n    'UPDATE': 1.2,\n    'AD': 1.5\n  };\n  \n  return Math.round(baseScore * (typeMultipliers[post.type] || 1));\n}\n\nfunction extractKeywords(content) {\n  // Simple keyword extraction\n  const words = content.toLowerCase().split(/\\s+/);\n  const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];\n  \n  return words\n    .filter(word => word.length > 3 && !stopWords.includes(word))\n    .reduce((acc, word) => {\n      acc[word] = (acc[word] || 0) + 1;\n      return acc;\n    }, {});\n}\n\n// Return all data for next nodes\nreturn {\n  postInfo,\n  analyticsData,\n  followerNotifications,\n  socialShare,\n  trendingData\n};"}, "id": "3", "name": "Process Social Post Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"url": "={{ process.env.ANALYTICS_API_URL }}/events", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "eventType", "value": "={{ $node[\"Process Social Post Data\"].json[\"analyticsData\"][\"type\"] }}"}, {"name": "postId", "value": "={{ $node[\"Process Social Post Data\"].json[\"analyticsData\"][\"postId\"] }}"}, {"name": "vendorId", "value": "={{ $node[\"Process Social Post Data\"].json[\"analyticsData\"][\"vendorId\"] }}"}, {"name": "contentType", "value": "={{ $node[\"Process Social Post Data\"].json[\"analyticsData\"][\"contentType\"] }}"}, {"name": "hasImages", "value": "={{ $node[\"Process Social Post Data\"].json[\"analyticsData\"][\"hasImages\"] }}"}, {"name": "contentLength", "value": "={{ $node[\"Process Social Post Data\"].json[\"analyticsData\"][\"contentLength\"] }}"}, {"name": "timestamp", "value": "={{ $node[\"Process Social Post Data\"].json[\"analyticsData\"][\"timestamp\"] }}"}]}, "options": {}}, "id": "4", "name": "Log Analytics Event", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 150], "credentials": {"httpBasicAuth": {"id": "1", "name": "Analytics API"}}}, {"parameters": {"url": "={{ process.env.API_URL }}/api/social/notify-followers", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "vendorId", "value": "={{ $node[\"Process Social Post Data\"].json[\"followerNotifications\"][\"vendorId\"] }}"}, {"name": "postId", "value": "={{ $node[\"Process Social Post Data\"].json[\"followerNotifications\"][\"postId\"] }}"}, {"name": "type", "value": "={{ $node[\"Process Social Post Data\"].json[\"followerNotifications\"][\"type\"] }}"}, {"name": "preview", "value": "={{ $node[\"Process Social Post Data\"].json[\"followerNotifications\"][\"preview\"] }}"}, {"name": "businessName", "value": "={{ $node[\"Process Social Post Data\"].json[\"followerNotifications\"][\"businessName\"] }}"}]}, "options": {}}, "id": "5", "name": "Notify Followers", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 300], "credentials": {"httpBasicAuth": {"id": "1", "name": "API Auth"}}}, {"parameters": {"url": "={{ process.env.SOCIAL_MEDIA_API_URL }}/share", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "={{ process.env.SOCIAL_MEDIA_API_KEY }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "platforms", "value": "[\"facebook\", \"twitter\", \"instagram\"]"}, {"name": "title", "value": "={{ $node[\"Process Social Post Data\"].json[\"socialShare\"][\"title\"] }}"}, {"name": "content", "value": "={{ $node[\"Process Social Post Data\"].json[\"socialShare\"][\"content\"] }}"}, {"name": "url", "value": "={{ $node[\"Process Social Post Data\"].json[\"socialShare\"][\"url\"] }}"}, {"name": "images", "value": "={{ JSON.stringify($node[\"Process Social Post Data\"].json[\"socialShare\"][\"images\"]) }}"}, {"name": "hashtags", "value": "={{ JSON.stringify($node[\"Process Social Post Data\"].json[\"socialShare\"][\"hashtags\"]) }}"}]}, "options": {}}, "id": "6", "name": "Social Media Share", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 450], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Social Media API"}}}, {"parameters": {"url": "={{ process.env.TRENDING_API_URL }}/analyze", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "vendorId", "value": "={{ $node[\"Process Social Post Data\"].json[\"trendingData\"][\"vendorId\"] }}"}, {"name": "businessName", "value": "={{ $node[\"Process Social Post Data\"].json[\"trendingData\"][\"businessName\"] }}"}, {"name": "postType", "value": "={{ $node[\"Process Social Post Data\"].json[\"trendingData\"][\"postType\"] }}"}, {"name": "engagement", "value": "={{ JSON.stringify($node[\"Process Social Post Data\"].json[\"trendingData\"][\"engagement\"]) }}"}, {"name": "keywords", "value": "={{ JSON.stringify($node[\"Process Social Post Data\"].json[\"trendingData\"][\"keywords\"]) }}"}]}, "options": {}}, "id": "7", "name": "Update Trending Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 600], "credentials": {"httpBasicAuth": {"id": "1", "name": "Trending API"}}}, {"parameters": {"resource": "email", "operation": "send", "fromEmail": "<EMAIL>", "toEmail": "={{ webhookData.vendorEmail }}", "subject": "Social Post Published Successfully", "text": "Your social post has been published and is being shared across our platform. Here are the details:\\n\\nPost Type: {{ webhookData.type }}\\nContent: {{ webhookData.content.substring(0, 100) }}{{ webhookData.content.length > 100 ? '...' : '' }}\\n\\nEngagement Projection: {{ $node[\"Process Social Post Data\"].json[\"trendingData\"][\"engagement\"][\"projected\"] }} interactions\\n\\nYou can view your post analytics in your vendor dashboard.\\n\\nBest regards,\\nThe IBT B2B Connect Team", "html": "<h2>Social Post Published Successfully! 📱</h2><p>Your social post has been published and is being shared across our platform.</p><div style=\"border: 1px solid #ddd; padding: 15px; margin: 15px 0;\"><h3>Post Details</h3><p><strong>Type:</strong> {{ webhookData.type }}</p><p><strong>Content:</strong> {{ webhookData.content.substring(0, 100) }}{{ webhookData.content.length > 100 ? '...' : '' }}</p><p><strong>Engagement Projection:</strong> {{ $node[\"Process Social Post Data\"].json[\"trendingData\"][\"engagement\"][\"projected\"] }} interactions</p></div><p>You can view your post analytics in your vendor dashboard.</p><p>Best regards,<br>The IBT B2B Connect Team</p>", "options": {}}, "id": "8", "name": "<PERSON><PERSON> Confirmation", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [900, 750], "credentials": {"sendGridApi": {"id": "1", "name": "SendGrid account"}}}], "pinData": {}, "connections": {"When clicking 'Test workflow'": {"main": [[{"node": "Social Feed Webhook", "type": "main", "index": 0}]]}, "Social Feed Webhook": {"main": [[{"node": "Process Social Post Data", "type": "main", "index": 0}]]}, "Process Social Post Data": {"main": [[{"node": "Log Analytics Event", "type": "main", "index": 0}, {"node": "Notify Followers", "type": "main", "index": 0}, {"node": "Social Media Share", "type": "main", "index": 0}, {"node": "Update Trending Analysis", "type": "main", "index": 0}, {"node": "<PERSON><PERSON> Confirmation", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "social-feed-posted-workflow", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "social-feed-posted", "name": "social-feed-posted"}], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "createdAt": "2024-01-01T00:00:00.000Z"}
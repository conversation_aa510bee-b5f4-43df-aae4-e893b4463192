import { Request, Response } from 'express';
import { prisma } from '../app';

export const securityController = {
  // Get security logs (placeholder implementation)
  async getSecurityLogs(req: Request, res: Response) {
    try {
      const { page = 1, limit = 50, eventType, userId } = req.query;

      const where: any = {};
      
      if (eventType) {
        where.eventType = eventType;
      }
      
      if (userId) {
        where.userId = userId;
      }

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

      const [logs, total] = await Promise.all([
        prisma.securityLog.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
                role: true
              }
            }
          },
          orderBy: {
            timestamp: 'desc'
          },
          skip,
          take: parseInt(limit as string)
        }),
        prisma.securityLog.count({ where })
      ]);

      res.json({
        logs,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          totalPages: Math.ceil(total / parseInt(limit as string))
        }
      });

    } catch (error) {
      console.error('Get security logs error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};
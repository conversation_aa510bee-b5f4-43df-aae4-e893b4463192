'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Navigation, 
  Filter,
  Search,
  X,
  RotateCcw,
  SlidersHorizontal
} from 'lucide-react';

interface GeoFilterBarProps {
  onFilterChange: (filters: GeoFilters) => void;
  initialFilters?: Partial<GeoFilters>;
  className?: string;
}

export interface GeoFilters {
  location?: {
    lat: number;
    lng: number;
    address?: string;
  };
  radius?: number;
  category?: string;
  vendorType?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'distance' | 'price' | 'rating' | 'newest';
  searchQuery?: string;
}

const radiusOptions = [
  { value: 500, label: '500m' },
  { value: 1000, label: '1km' },
  { value: 2000, label: '2km' },
  { value: 5000, label: '5km' },
  { value: 10000, label: '10km' },
  { value: 25000, label: '25km' }
];

const sortOptions = [
  { value: 'distance', label: 'Distance' },
  { value: 'price', label: 'Price: Low to High' },
  { value: 'rating', label: 'Rating' },
  { value: 'newest', label: 'Newest' }
];

export default function GeoFilterBar({
  onFilterChange,
  initialFilters = {},
  className = ''
}: GeoFilterBarProps) {
  const [filters, setFilters] = useState<GeoFilters>(initialFilters);
  const [expanded, setExpanded] = useState(false);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);

  useEffect(() => {
    // Get user's location on component mount
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          setUserLocation(location);
          
          // Auto-set location if not already set
          if (!filters.location) {
            const newFilters = { ...filters, location };
            setFilters(newFilters);
            onFilterChange(newFilters);
          }
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  }, []);

  useEffect(() => {
    onFilterChange(filters);
  }, [filters, onFilterChange]);

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.');
      return;
    }

    setIsLoadingLocation(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const location = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        setUserLocation(location);
        
        const newFilters = {
          ...filters,
          location
        };
        setFilters(newFilters);
        onFilterChange(newFilters);
        setIsLoadingLocation(false);
      },
      (error) => {
        console.error('Error getting location:', error);
        alert('Unable to get your location. Please enter it manually.');
        setIsLoadingLocation(false);
      }
    );
  };

  const updateFilter = (key: keyof GeoFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
  };

  const removeFilter = (key: keyof GeoFilters) => {
    const newFilters = { ...filters };
    delete newFilters[key];
    setFilters(newFilters);
  };

  const clearAllFilters = () => {
    setFilters({});
  };

  const hasActiveFilters = Object.keys(filters).some(key => {
    const value = filters[key as keyof GeoFilters];
    return value !== undefined && value !== null && value !== '';
  });

  const getActiveFiltersCount = () => {
    return Object.keys(filters).filter(key => {
      const value = filters[key as keyof GeoFilters];
      return value !== undefined && value !== null && value !== '';
    }).length;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Filter Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search products, vendors, or categories..."
                  value={filters.searchQuery || ''}
                  onChange={(e) => updateFilter('searchQuery', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Location */}
            <div className="flex gap-2">
              <div className="relative flex-1 min-w-[200px]">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Location"
                  value={filters.location?.address || `${filters.location?.lat?.toFixed(4)}, ${filters.location?.lng?.toFixed(4)}` || ''}
                  readOnly
                  className="pl-10"
                />
              </div>
              <Button
                variant="outline"
                onClick={getCurrentLocation}
                disabled={isLoadingLocation}
                size="sm"
              >
                {isLoadingLocation ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                ) : (
                  <Navigation className="h-4 w-4" />
                )}
              </Button>
            </div>

            {/* Expand/Collapse */}
            <Button
              variant="outline"
              onClick={() => setExpanded(!expanded)}
              className="whitespace-nowrap"
            >
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Filters {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
            </Button>
          </div>

          {/* Active Filters */}
          {hasActiveFilters && (
            <div className="flex flex-wrap gap-2 mt-4">
              {filters.location && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  Location
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter('location')}
                  />
                </Badge>
              )}
              {filters.radius && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Radius: {filters.radius}m
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter('radius')}
                  />
                </Badge>
              )}
              {filters.category && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Category: {filters.category}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter('category')}
                  />
                </Badge>
              )}
              {filters.vendorType && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Type: {filters.vendorType}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter('vendorType')}
                  />
                </Badge>
              )}
              {filters.minPrice && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Min: ${filters.minPrice}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter('minPrice')}
                  />
                </Badge>
              )}
              {filters.maxPrice && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Max: ${filters.maxPrice}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter('maxPrice')}
                  />
                </Badge>
              )}
              {filters.sortBy && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Sort: {filters.sortBy}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter('sortBy')}
                  />
                </Badge>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="h-6 px-2"
              >
                <RotateCcw className="h-3 w-3" />
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Expanded Filters */}
      {expanded && (
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Radius */}
              <div>
                <Label>Search Radius</Label>
                <Select 
                  value={filters.radius?.toString() || ''} 
                  onValueChange={(value) => updateFilter('radius', parseInt(value))}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select radius" />
                  </SelectTrigger>
                  <SelectContent>
                    {radiusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Category */}
              <div>
                <Label>Category</Label>
                <Select 
                  value={filters.category || ''} 
                  onValueChange={(value) => updateFilter('category', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="All categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All categories</SelectItem>
                    <SelectItem value="food-beverages">Food & Beverages</SelectItem>
                    <SelectItem value="electronics">Electronics</SelectItem>
                    <SelectItem value="clothing">Clothing & Accessories</SelectItem>
                    <SelectItem value="home-services">Home Services</SelectItem>
                    <SelectItem value="professional-services">Professional Services</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Vendor Type */}
              <div>
                <Label>Vendor Type</Label>
                <Select 
                  value={filters.vendorType || ''} 
                  onValueChange={(value) => updateFilter('vendorType', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All types</SelectItem>
                    <SelectItem value="FOOD">Food & Beverages</SelectItem>
                    <SelectItem value="ECOMMERCE">E-commerce</SelectItem>
                    <SelectItem value="SERVICE">Services</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Price Range */}
              <div>
                <Label>Price Range</Label>
                <div className="flex gap-2 mt-1">
                  <Input
                    type="number"
                    placeholder="Min"
                    value={filters.minPrice || ''}
                    onChange={(e) => updateFilter('minPrice', e.target.value ? parseFloat(e.target.value) : undefined)}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    value={filters.maxPrice || ''}
                    onChange={(e) => updateFilter('maxPrice', e.target.value ? parseFloat(e.target.value) : undefined)}
                  />
                </div>
              </div>

              {/* Sort By */}
              <div>
                <Label>Sort By</Label>
                <Select 
                  value={filters.sortBy || ''} 
                  onValueChange={(value) => updateFilter('sortBy', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Manual Location */}
              <div>
                <Label>Manual Location</Label>
                <div className="flex gap-2 mt-1">
                  <Input
                    type="number"
                    step="any"
                    placeholder="Latitude"
                    value={filters.location?.lat || ''}
                    onChange={(e) => {
                      const lat = e.target.value ? parseFloat(e.target.value) : undefined;
                      updateFilter('location', lat ? { 
                        ...filters.location!, 
                        lat 
                      } : undefined);
                    }}
                  />
                  <Input
                    type="number"
                    step="any"
                    placeholder="Longitude"
                    value={filters.location?.lng || ''}
                    onChange={(e) => {
                      const lng = e.target.value ? parseFloat(e.target.value) : undefined;
                      updateFilter('location', lng ? { 
                        ...filters.location!, 
                        lng 
                      } : undefined);
                    }}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Smartphone, 
  MessageSquare, 
  ShoppingCart, 
  Search, 
  User,
  ArrowRight,
  Phone,
  HelpCircle
} from 'lucide-react';

export default function USSDPage() {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [currentStep, setCurrentStep] = useState(0);
  const [ussdResponse, setUssdResponse] = useState('');
  const [loading, setLoading] = useState(false);

  const ussdSteps = [
    {
      title: 'Welcome to IBT B2B Connect',
      description: 'Access our marketplace directly from your mobile phone',
      code: '*123#',
      options: [
        { key: '1', text: 'Login to your account' },
        { key: '2', text: 'Register new account' },
        { key: '3', text: 'Search for products' },
        { key: '4', text: 'View my orders' },
        { key: '5', text: 'Get help' }
      ]
    },
    {
      title: 'Login',
      description: 'Enter your credentials to access your account',
      input: 'email',
      placeholder: 'Enter your email address'
    },
    {
      title: 'Password',
      description: 'Enter your password to complete login',
      input: 'password',
      placeholder: 'Enter your password'
    },
    {
      title: 'Product Search',
      description: 'Search for products by name or category',
      input: 'search',
      placeholder: 'What are you looking for?'
    }
  ];

  const simulateUSSD = async (step: number, input?: string) => {
    setLoading(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const responses = [
      'Welcome to IBT B2B Connect!\n1. Login\n2. Register\n3. Search Products\n4. My Orders\n5. Help',
      'Please enter your email address:',
      'Please enter your password:',
      'Please enter search term:',
      'Login successful! Welcome back.',
      'Search results found: 5 products matching your query.',
      'No products found. Please try another search term.'
    ];

    let responseIndex = step;
    if (step === 2 && input === '<EMAIL>') responseIndex = 4;
    if (step === 3 && input === 'password') responseIndex = 4;
    if (step === 4 && input === 'food') responseIndex = 5;
    if (step === 4 && input === 'xyz') responseIndex = 6;

    setUssdResponse(responses[responseIndex]);
    setLoading(false);
  };

  const handleStepAction = (action: string) => {
    if (action === 'next') {
      if (currentStep < ussdSteps.length - 1) {
        setCurrentStep(currentStep + 1);
        simulateUSSD(currentStep + 1);
      }
    } else if (action === 'prev') {
      if (currentStep > 0) {
        setCurrentStep(currentStep - 1);
        simulateUSSD(currentStep - 1);
      }
    } else if (action === 'reset') {
      setCurrentStep(0);
      setUssdResponse('');
      setPhoneNumber('');
    }
  };

  const handleDial = () => {
    if (!phoneNumber) {
      alert('Please enter your phone number');
      return;
    }
    
    setCurrentStep(0);
    simulateUSSD(0);
  };

  const currentUssdStep = ussdSteps[currentStep];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Smartphone className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">USSD Access</h1>
            </div>
            <Button variant="outline" onClick={() => handleStepAction('reset')}>
              Reset Demo
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Instructions Panel */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Phone className="h-5 w-5 mr-2" />
                  How to Use USSD
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="font-semibold mb-2">What is USSD?</h3>
                  <p className="text-gray-600">
                    USSD (Unstructured Supplementary Service Data) allows you to access our marketplace 
                    services directly from your mobile phone without internet connection.
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Getting Started</h3>
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <div className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold">
                        1
                      </div>
                      <div>
                        <p className="font-medium">Dial the USSD code</p>
                        <p className="text-gray-600 text-sm">Enter *123# on your phone</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold">
                        2
                      </div>
                      <div>
                        <p className="font-medium">Follow the menu</p>
                        <p className="text-gray-600 text-sm">Select options by entering numbers</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold">
                        3
                      </div>
                      <div>
                        <p className="font-medium">Complete transactions</p>
                        <p className="text-gray-600 text-sm">Browse, order, and pay securely</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Available Services</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <User className="h-4 w-4 text-blue-600" />
                      <span className="text-sm">Account Management</span>
                    </div>
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <Search className="h-4 w-4 text-green-600" />
                      <span className="text-sm">Product Search</span>
                    </div>
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <ShoppingCart className="h-4 w-4 text-purple-600" />
                      <span className="text-sm">Order Management</span>
                    </div>
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <HelpCircle className="h-4 w-4 text-orange-600" />
                      <span className="text-sm">Customer Support</span>
                    </div>
                  </div>
                </div>

                <Alert>
                  <AlertDescription>
                    <strong>Note:</strong> This is a simulation. In production, you would dial the actual 
                    USSD code on your mobile phone to access these services.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>

          {/* USSD Simulator */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  USSD Simulator
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Phone Number Input */}
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Your Phone Number
                  </label>
                  <div className="flex space-x-2">
                    <Input
                      type="tel"
                      placeholder="+****************"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                    />
                    <Button onClick={handleDial} disabled={!phoneNumber}>
                      Dial
                    </Button>
                  </div>
                </div>

                {/* USSD Code Display */}
                <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
                  <div className="flex items-center justify-between mb-2">
                    <span>USSD Code:</span>
                    <Badge variant="secondary" className="bg-green-600 text-white">
                      {currentUssdStep.code || '*123#'}
                    </Badge>
                  </div>
                  <div className="text-green-300">
                    Status: {currentStep === 0 ? 'Connected' : 'In Session'}
                  </div>
                </div>

                {/* Current Step */}
                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">{currentUssdStep.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{currentUssdStep.description}</p>

                  {currentUssdStep.input && (
                    <Input
                      type={currentUssdStep.input === 'password' ? 'password' : 'text'}
                      placeholder={currentUssdStep.placeholder}
                      className="mb-4"
                    />
                  )}

                  {currentUssdStep.options && (
                    <div className="space-y-2">
                      {currentUssdStep.options.map((option) => (
                        <div
                          key={option.key}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100"
                        >
                          <span className="font-medium">{option.key}. {option.text}</span>
                          <ArrowRight className="h-4 w-4 text-gray-400" />
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* USSD Response */}
                {ussdResponse && (
                  <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-4">
                    <div className="text-sm text-gray-600 mb-2">USSD Response:</div>
                    <div className="bg-white p-3 rounded font-mono text-sm whitespace-pre-line">
                      {ussdResponse}
                    </div>
                  </div>
                )}

                {/* Navigation */}
                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => handleStepAction('prev')}
                    disabled={currentStep === 0}
                  >
                    Previous
                  </Button>
                  <Button
                    onClick={() => handleStepAction('next')}
                    disabled={loading || currentStep >= ussdSteps.length - 1}
                  >
                    {loading ? 'Processing...' : 'Next'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">USSD Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6 text-center">
                <User className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold mb-2">User Authentication</h3>
                <p className="text-sm text-gray-600">Secure login and registration</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <Search className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Product Search</h3>
                <p className="text-sm text-gray-600">Find products by category or name</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <ShoppingCart className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Order Management</h3>
                <p className="text-sm text-gray-600">Place and track orders</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <HelpCircle className="h-12 w-12 text-orange-600 mx-auto mb-4" />
                <h3 className="font-semibold mb-2">24/7 Support</h3>
                <p className="text-sm text-gray-600">Get help anytime, anywhere</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
import { Request, Response } from 'express';
import { prisma } from '../app';

export const orderController = {
  // Get user's orders
  async getUserOrders(req: Request, res: Response) {
    try {
      const userId = req.user!.userId;
      const { status } = req.query;

      const where: any = { userId };
      
      if (status) {
        where.status = status;
      }

      const orders = await prisma.order.findMany({
        where,
        include: {
          vendor: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  name: true
                }
              }
            }
          },
          items: {
            include: {
              product: {
                include: {
                  category: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      res.json(orders);
    } catch (error) {
      console.error('Get orders error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Get order by ID
  async getOrderById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user!.userId;

      const order = await prisma.order.findUnique({
        where: { id },
        include: {
          vendor: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  name: true
                }
              }
            }
          },
          items: {
            include: {
              product: {
                include: {
                  category: true,
                  vendor: {
                    select: {
                      id: true,
                      businessName: true,
                      vendorType: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      // Check if user is order owner or vendor
      if (order.userId !== userId && order.vendor.userId !== userId && req.user!.role !== 'ADMIN') {
        return res.status(403).json({ error: 'Access denied' });
      }

      res.json(order);
    } catch (error) {
      console.error('Get order error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Create order
  async createOrder(req: Request, res: Response) {
    try {
      const { paymentMethod, vendorId } = req.body;
      const userId = req.user!.userId;

      if (!paymentMethod || !vendorId) {
        return res.status(400).json({ error: 'Payment method and vendor ID are required' });
      }

      // Get user's cart
      const cart = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            include: {
              product: true
            }
          }
        }
      });

      if (!cart || cart.items.length === 0) {
        return res.status(400).json({ error: 'Cart is empty' });
      }

      // Verify all items are from the same vendor
      const vendorIds = [...new Set(cart.items.map(item => item.product.vendorId))];
      if (vendorIds.length > 1 || vendorIds[0] !== vendorId) {
        return res.status(400).json({ error: 'All items must be from the same vendor' });
      }

      // Check stock availability
      for (const item of cart.items) {
        if (item.product.stock < item.qty) {
          return res.status(400).json({ 
            error: `Insufficient stock for product: ${item.product.title}` 
          });
        }
      }

      // Calculate total
      const total = cart.items.reduce((sum, item) => sum + (item.qty * item.product.price), 0);

      // Create order
      const order = await prisma.order.create({
        data: {
          userId,
          vendorId,
          total,
          paymentMethod,
          status: 'PENDING'
        }
      });

      // Create order items and update product stock
      const orderItems = await Promise.all(
        cart.items.map(item => 
          prisma.orderItem.create({
            data: {
              orderId: order.id,
              productId: item.productId,
              qty: item.qty,
              unitPrice: item.product.price
            }
          })
        )
      );

      // Update product stock
      await Promise.all(
        cart.items.map(item => 
          prisma.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                decrement: item.qty
              }
            }
          })
        )
      );

      // Clear cart
      await prisma.cartItem.deleteMany({
        where: { cartId: cart.id }
      });

      // Get complete order with details
      const completeOrder = await prisma.order.findUnique({
        where: { id: order.id },
        include: {
          vendor: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  name: true
                }
              }
            }
          },
          items: {
            include: {
              product: {
                include: {
                  category: true
                }
              }
            }
          }
        }
      });

      // Log analytics event
      await prisma.analyticsEvent.create({
        data: {
          entityId: userId,
          type: 'VENDOR_SALE',
          data: JSON.stringify({
            orderId: order.id,
            vendorId,
            total,
            paymentMethod
          })
        }
      });

      res.status(201).json({
        message: 'Order created successfully',
        order: completeOrder
      });

    } catch (error) {
      console.error('Create order error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};
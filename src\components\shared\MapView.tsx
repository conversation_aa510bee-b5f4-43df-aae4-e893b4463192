'use client';

import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, CircleMarker } from 'react-leaflet';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Filter, 
  Navigation,
  Maximize2,
  Layers
} from 'lucide-react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface MapViewProps {
  center?: [number, number];
  zoom?: number;
  markers?: Array<{
    id: string;
    position: [number, number];
    title: string;
    type?: string;
    category?: string;
    price?: number;
    vendor?: string;
  }>;
  userLocation?: [number, number];
  radius?: number;
  height?: string;
  showControls?: boolean;
  onMarkerClick?: (marker: any) => void;
  onLocationSelect?: (location: [number, number]) => void;
}

export default function MapView({
  center = [13.9094, -60.9789], // Default to Saint Lucia
  zoom = 13,
  markers = [],
  userLocation,
  radius,
  height = '400px',
  showControls = true,
  onMarkerClick,
  onLocationSelect
}: MapViewProps) {
  const [mapCenter, setMapCenter] = useState<[number, number]>(center);
  const [mapZoom, setMapZoom] = useState(zoom);
  const [selectedMarker, setSelectedMarker] = useState<any>(null);
  const [showUserLocation, setShowUserLocation] = useState(false);

  useEffect(() => {
    if (userLocation) {
      setMapCenter(userLocation);
      setMapZoom(15);
      setShowUserLocation(true);
    }
  }, [userLocation]);

  const handleMapClick = (e: any) => {
    if (onLocationSelect) {
      const { lat, lng } = e.latlng;
      onLocationSelect([lat, lng]);
    }
  };

  const getCategoryColor = (type?: string) => {
    switch (type) {
      case 'FOOD': return 'bg-green-100 text-green-800';
      case 'ECOMMERCE': return 'bg-blue-100 text-blue-800';
      case 'SERVICE': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (type?: string) => {
    switch (type) {
      case 'FOOD': return '🍽️';
      case 'ECOMMERCE': return '🛍️';
      case 'SERVICE': return '🔧';
      default: return '📍';
    }
  };

  const locateUser = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userPos: [number, number] = [position.coords.latitude, position.coords.longitude];
          setMapCenter(userPos);
          setMapZoom(15);
          setShowUserLocation(true);
          if (onLocationSelect) {
            onLocationSelect(userPos);
          }
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  };

  return (
    <div className="relative">
      <div style={{ height }} className="rounded-lg overflow-hidden border">
        <MapContainer
          center={mapCenter}
          zoom={mapZoom}
          className="h-full w-full"
          onClick={handleMapClick}
        >
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />
          
          {/* User location marker */}
          {showUserLocation && userLocation && (
            <>
              <CircleMarker
                center={userLocation}
                radius={radius || 1000} // 1km radius
                color="#3b82f6"
                fillColor="#3b82f6"
                fillOpacity={0.1}
                weight={2}
              />
              <Marker position={userLocation}>
                <Popup>
                  <div className="text-center">
                    <p className="font-medium">Your Location</p>
                    <p className="text-sm text-gray-600">
                      {userLocation[0].toFixed(4)}, {userLocation[1].toFixed(4)}
                    </p>
                    {radius && (
                      <p className="text-sm text-gray-600">
                        Search radius: {radius}m
                      </p>
                    )}
                  </div>
                </Popup>
              </Marker>
            </>
          )}

          {/* Product/Vendor markers */}
          {markers.map((marker) => (
            <Marker
              key={marker.id}
              position={marker.position}
              eventHandlers={{
                click: () => {
                  setSelectedMarker(marker);
                  if (onMarkerClick) {
                    onMarkerClick(marker);
                  }
                }
              }}
            >
              <Popup>
                <div className="min-w-[200px]">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg">{getCategoryIcon(marker.type)}</span>
                    <h3 className="font-medium text-sm">{marker.title}</h3>
                  </div>
                  
                  {marker.category && (
                    <Badge className={`text-xs mb-2 ${getCategoryColor(marker.type)}`}>
                      {marker.category}
                    </Badge>
                  )}
                  
                  {marker.price && (
                    <p className="text-sm font-medium text-gray-900 mb-1">
                      ${marker.price.toFixed(2)}
                    </p>
                  )}
                  
                  {marker.vendor && (
                    <p className="text-xs text-gray-600 mb-2">
                      🏪 {marker.vendor}
                    </p>
                  )}
                  
                  <p className="text-xs text-gray-500 mb-3">
                    📍 {marker.position[0].toFixed(4)}, {marker.position[1].toFixed(4)}
                  </p>
                  
                  <Button 
                    size="sm" 
                    className="w-full"
                    onClick={() => {
                      if (onMarkerClick) {
                        onMarkerClick(marker);
                      }
                    }}
                  >
                    View Details
                  </Button>
                </div>
              </Popup>
            </Marker>
          ))}
        </MapContainer>
      </div>

      {/* Map Controls */}
      {showControls && (
        <div className="absolute top-4 right-4 z-[1000] space-y-2">
          <Card className="w-48">
            <CardContent className="p-3">
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={locateUser}
                >
                  <Navigation className="h-4 w-4 mr-2" />
                  My Location
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setMapZoom(mapZoom + 1)}
                >
                  <Maximize2 className="h-4 w-4 mr-2" />
                  Zoom In
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setMapZoom(Math.max(1, mapZoom - 1))}
                >
                  <Maximize2 className="h-4 w-4 mr-2 rotate-180" />
                  Zoom Out
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="w-48">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center">
                <Layers className="h-4 w-4 mr-2" />
                Legend
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3">
              <div className="space-y-2 text-xs">
                <div className="flex items-center gap-2">
                  <span className="text-lg">🍽️</span>
                  <span>Food & Beverages</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-lg">🛍️</span>
                  <span>E-commerce</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-lg">🔧</span>
                  <span>Services</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>Your location</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Selected Marker Info */}
      {selectedMarker && (
        <Card className="absolute bottom-4 left-4 z-[1000] w-80">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <MapPin className="h-5 w-5 mr-2" />
              {selectedMarker.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {selectedMarker.category && (
                <Badge className={`text-xs ${getCategoryColor(selectedMarker.type)}`}>
                  {selectedMarker.category}
                </Badge>
              )}
              
              {selectedMarker.price && (
                <p className="text-lg font-bold text-gray-900">
                  ${selectedMarker.price.toFixed(2)}
                </p>
              )}
              
              {selectedMarker.vendor && (
                <p className="text-sm text-gray-600">
                  🏪 {selectedMarker.vendor}
                </p>
              )}
              
              <div className="flex gap-2 pt-2">
                <Button 
                  size="sm" 
                  className="flex-1"
                  onClick={() => {
                    if (onMarkerClick) {
                      onMarkerClick(selectedMarker);
                    }
                  }}
                >
                  View Details
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setSelectedMarker(null)}
                >
                  Close
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Marker Count */}
      {markers.length > 0 && (
        <div className="absolute top-4 left-4 z-[1000]">
          <Card>
            <CardContent className="p-3">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">
                  {markers.length} location{markers.length !== 1 ? 's' : ''} found
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
import { Request, Response } from 'express';
import { prisma } from '../app';

export const vendorController = {
  // Get all vendors
  async getAllVendors(req: Request, res: Response) {
    try {
      const { status, type } = req.query;
      
      const where: any = {};
      
      if (status) {
        where.status = status;
      }
      
      if (type) {
        where.vendorType = type;
      }

      const vendors = await prisma.vendor.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              latitude: true,
              longitude: true
            }
          },
          products: {
            select: {
              id: true,
              title: true,
              price: true,
              stock: true
            }
          },
          _count: {
            select: {
              products: true,
              orders: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      res.json(vendors);
    } catch (error) {
      console.error('Get vendors error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Get vendor by ID
  async getVendorById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const vendor = await prisma.vendor.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              latitude: true,
              longitude: true
            }
          },
          products: {
            include: {
              category: true
            }
          },
          socialFeeds: {
            orderBy: {
              timestamp: 'desc'
            },
            take: 10
          }
        }
      });

      if (!vendor) {
        return res.status(404).json({ error: 'Vendor not found' });
      }

      res.json(vendor);
    } catch (error) {
      console.error('Get vendor error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Create vendor
  async createVendor(req: Request, res: Response) {
    try {
      const { businessName, vendorType, description, latitude, longitude } = req.body;
      const userId = req.user!.userId;

      // Check if vendor already exists for this user
      const existingVendor = await prisma.vendor.findUnique({
        where: { userId }
      });

      if (existingVendor) {
        return res.status(400).json({ error: 'Vendor already exists for this user' });
      }

      // Create vendor
      const vendor = await prisma.vendor.create({
        data: {
          userId,
          businessName,
          vendorType,
          description,
          latitude: latitude || null,
          longitude: longitude || null
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true
            }
          }
        }
      });

      // Update user role to VENDOR
      await prisma.user.update({
        where: { id: userId },
        data: { role: 'VENDOR' }
      });

      res.status(201).json({
        message: 'Vendor created successfully',
        vendor
      });

    } catch (error) {
      console.error('Create vendor error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Update vendor
  async updateVendor(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { businessName, vendorType, description, latitude, longitude, status } = req.body;
      const userId = req.user!.userId;

      // Find vendor and verify ownership
      const vendor = await prisma.vendor.findUnique({
        where: { id }
      });

      if (!vendor) {
        return res.status(404).json({ error: 'Vendor not found' });
      }

      // Check if user is vendor owner or admin
      if (vendor.userId !== userId && req.user!.role !== 'ADMIN') {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Update vendor
      const updatedVendor = await prisma.vendor.update({
        where: { id },
        data: {
          ...(businessName && { businessName }),
          ...(vendorType && { vendorType }),
          ...(description !== undefined && { description }),
          ...(latitude !== undefined && { latitude }),
          ...(longitude !== undefined && { longitude }),
          ...(status && req.user!.role === 'ADMIN' && { status })
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true
            }
          }
        }
      });

      res.json({
        message: 'Vendor updated successfully',
        vendor: updatedVendor
      });

    } catch (error) {
      console.error('Update vendor error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};
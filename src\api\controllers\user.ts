import { Request, Response } from 'express';
import { prisma } from '../app';

export const userController = {
  // Get user locale
  async getUserLocale(req: Request, res: Response) {
    try {
      const userId = req.user!.userId;

      let locale = await prisma.locale.findUnique({
        where: { userId }
      });

      // Create default locale if it doesn't exist
      if (!locale) {
        locale = await prisma.locale.create({
          data: {
            userId,
            language: 'EN',
            currency: 'XCD'
          }
        });
      }

      res.json({
        locale: {
          language: locale.language,
          currency: locale.currency
        }
      });

    } catch (error) {
      console.error('Get user locale error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Update user locale
  async updateUserLocale(req: Request, res: Response) {
    try {
      const { language, currency } = req.body;
      const userId = req.user!.userId;

      if (!language && !currency) {
        return res.status(400).json({ error: 'Language or currency is required' });
      }

      // Get or create locale
      let locale = await prisma.locale.findUnique({
        where: { userId }
      });

      if (!locale) {
        locale = await prisma.locale.create({
          data: {
            userId,
            language: language || 'EN',
            currency: currency || 'XCD'
          }
        });
      } else {
        locale = await prisma.locale.update({
          where: { userId },
          data: {
            ...(language && { language }),
            ...(currency && { currency })
          }
        });
      }

      res.json({
        message: 'Locale updated successfully',
        locale: {
          language: locale.language,
          currency: locale.currency
        }
      });

    } catch (error) {
      console.error('Update user locale error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};
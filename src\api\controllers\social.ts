import { Request, Response } from 'express';
import { prisma } from '../app';

export const socialController = {
  // Get social feed
  async getSocialFeed(req: Request, res: Response) {
    try {
      const { page = 1, limit = 20, type, vendorId } = req.query;

      const where: any = {};
      
      if (type) {
        where.type = type;
      }
      
      if (vendorId) {
        where.vendorId = vendorId;
      }

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

      const [posts, total] = await Promise.all([
        prisma.socialFeed.findMany({
          where,
          include: {
            vendor: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          },
          orderBy: {
            timestamp: 'desc'
          },
          skip,
          take: parseInt(limit as string)
        }),
        prisma.socialFeed.count({ where })
      ]);

      // Parse images JSON for each post
      const postsWithImages = posts.map(post => ({
        ...post,
        images: post.images ? JSON.parse(post.images) : []
      }));

      res.json({
        posts: postsWithImages,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          totalPages: Math.ceil(total / parseInt(limit as string))
        }
      });

    } catch (error) {
      console.error('Get social feed error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Get vendor's social feed
  async getVendorFeed(req: Request, res: Response) {
    try {
      const { vendorId } = req.params;
      const { page = 1, limit = 20 } = req.query;

      // Check if vendor exists
      const vendor = await prisma.vendor.findUnique({
        where: { id: vendorId }
      });

      if (!vendor) {
        return res.status(404).json({ error: 'Vendor not found' });
      }

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

      const [posts, total] = await Promise.all([
        prisma.socialFeed.findMany({
          where: { vendorId },
          include: {
            vendor: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          },
          orderBy: {
            timestamp: 'desc'
          },
          skip,
          take: parseInt(limit as string)
        }),
        prisma.socialFeed.count({ where: { vendorId } })
      ]);

      // Parse images JSON for each post
      const postsWithImages = posts.map(post => ({
        ...post,
        images: post.images ? JSON.parse(post.images) : []
      }));

      res.json({
        posts: postsWithImages,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          totalPages: Math.ceil(total / parseInt(limit as string))
        }
      });

    } catch (error) {
      console.error('Get vendor feed error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Create social post
  async createSocialPost(req: Request, res: Response) {
    try {
      const { type, content, images } = req.body;
      const userId = req.user!.userId;

      if (!type || !content) {
        return res.status(400).json({ error: 'Type and content are required' });
      }

      // Get vendor for this user
      const vendor = await prisma.vendor.findUnique({
        where: { userId }
      });

      if (!vendor) {
        return res.status(404).json({ error: 'Vendor not found' });
      }

      // Create social post
      const post = await prisma.socialFeed.create({
        data: {
          vendorId: vendor.id,
          type,
          content,
          images: images && images.length > 0 ? JSON.stringify(images) : null
        },
        include: {
          vendor: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });

      // Log analytics event
      await prisma.analyticsEvent.create({
        data: {
          entityId: userId,
          type: 'SOCIAL_FEED',
          data: JSON.stringify({
            postId: post.id,
            type,
            vendorId: vendor.id
          })
        }
      });

      const postWithImages = {
        ...post,
        images: post.images ? JSON.parse(post.images) : []
      };

      res.status(201).json({
        message: 'Social post created successfully',
        post: postWithImages
      });

    } catch (error) {
      console.error('Create social post error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};
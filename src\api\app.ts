import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { PrismaClient } from '@prisma/client';

// Import routes
import testRoutes from './routes/test';
// import authRoutes from './routes/auth';
// import vendorRoutes from './routes/vendor';
// import categoryRoutes from './routes/category';
// import productRoutes from './routes/product';
// import cartRoutes from './routes/cart';
// import orderRoutes from './routes/order';
// import socialRoutes from './routes/social';
// import paymentRoutes from './routes/payment';
// import userRoutes from './routes/user';
// import securityRoutes from './routes/security';
// import analyticsRoutes from './routes/analytics';

// Initialize Prisma client
export const prisma = new PrismaClient();

const app = express();

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// API Routes
app.use('/api/test', testRoutes);
// app.use('/api/auth', authRoutes);
// app.use('/api/vendors', vendorRoutes);
// app.use('/api/categories', categoryRoutes);
// app.use('/api/products', productRoutes);
// app.use('/api/cart', cartRoutes);
// app.use('/api/orders', orderRoutes);
// app.use('/api/social', socialRoutes);
// app.use('/api/payments', paymentRoutes);
// app.use('/api/user', userRoutes);
// app.use('/api/security', securityRoutes);
// app.use('/api/analytics', analyticsRoutes);

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

export default app;

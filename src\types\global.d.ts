// Temporary type declarations to resolve TypeScript errors
// These should be replaced with proper @types packages when network allows

declare module 'express' {
  import { IncomingMessage, ServerResponse } from 'http';
  
  export interface Request extends IncomingMessage {
    body?: any;
    params?: any;
    query?: any;
    headers: any;
  }
  
  export interface Response extends ServerResponse {
    json(obj: any): void;
    status(code: number): Response;
    send(data: any): void;
  }
  
  export interface NextFunction {
    (err?: any): void;
  }
  
  export interface Application {
    use(...args: any[]): void;
    get(path: string, ...handlers: any[]): void;
    post(path: string, ...handlers: any[]): void;
    put(path: string, ...handlers: any[]): void;
    delete(path: string, ...handlers: any[]): void;
    listen(port: number, callback?: () => void): void;
  }
  
  function express(): Application;
  namespace express {
    function json(options?: any): any;
    function urlencoded(options?: any): any;
    export { Request, Response, NextFunction, Application };
  }
  
  export = express;
}

declare module 'cors' {
  interface CorsOptions {
    origin?: string | boolean | RegExp | (string | RegExp)[] | ((origin: string, callback: (err: Error | null, allow?: boolean) => void) => void);
    credentials?: boolean;
    methods?: string | string[];
    allowedHeaders?: string | string[];
    exposedHeaders?: string | string[];
    maxAge?: number;
    preflightContinue?: boolean;
    optionsSuccessStatus?: number;
  }
  
  function cors(options?: CorsOptions): any;
  export = cors;
}

declare module 'helmet' {
  function helmet(options?: any): any;
  export = helmet;
}

declare module 'morgan' {
  function morgan(format: string, options?: any): any;
  export = morgan;
}

// Global Node.js types
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV?: string;
      FRONTEND_URL?: string;
      DATABASE_URL?: string;
      [key: string]: string | undefined;
    }
    
    interface Process {
      env: ProcessEnv;
    }
  }
  
  var process: NodeJS.Process;
}

export {};

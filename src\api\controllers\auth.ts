import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { prisma } from '../app';
import { authValidation } from '../middleware/validation';

export const authController = {
  // User signup
  async signup(req: Request, res: Response) {
    try {
      const { email, password, name, latitude, longitude } = req.body;

      // Validate input
      if (!email || !password || !name) {
        return res.status(400).json({ error: 'Email, password, and name are required' });
      }

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        return res.status(400).json({ error: 'User already exists' });
      }

      // Hash password
      const passwordHash = await bcrypt.hash(password, 12);

      // Create user
      const user = await prisma.user.create({
        data: {
          email,
          passwordHash,
          name,
          latitude: latitude || null,
          longitude: longitude || null
        },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          latitude: true,
          longitude: true,
          createdAt: true
        }
      });

      // Generate JWT token
      const token = jwt.sign(
        { userId: user.id, email: user.email, role: user.role },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: '7d' }
      );

      res.status(201).json({
        message: 'User created successfully',
        user,
        token
      });

    } catch (error) {
      console.error('Signup error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // User login
  async login(req: Request, res: Response) {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({ error: 'Email and password are required' });
      }

      // Find user
      const user = await prisma.user.findUnique({
        where: { email }
      });

      if (!user) {
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.passwordHash);
      if (!isValidPassword) {
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Generate JWT token
      const token = jwt.sign(
        { userId: user.id, email: user.email, role: user.role },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: '7d' }
      );

      // Log security event
      await prisma.securityLog.create({
        data: {
          userId: user.id,
          eventType: 'LOGIN',
          status: 'SUCCESS'
        }
      });

      res.json({
        message: 'Login successful',
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          latitude: user.latitude,
          longitude: user.longitude
        },
        token
      });

    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // USSD session start
  async ussdStart(req: Request, res: Response) {
    try {
      const { phoneNumber, serviceCode } = req.body;

      if (!phoneNumber) {
        return res.status(400).json({ error: 'Phone number is required' });
      }

      // Create or update USSD session
      const session = await prisma.uSSDSession.upsert({
        where: { sessionId: phoneNumber },
        update: {
          currentMenu: 'MAIN',
          collectedData: JSON.stringify({})
        },
        create: {
          sessionId: phoneNumber,
          phoneNumber,
          currentMenu: 'MAIN',
          collectedData: JSON.stringify({})
        }
      });

      // Get main menu
      const mainMenu = await prisma.uSSDMenu.findUnique({
        where: { code: 'MAIN' }
      });

      if (!mainMenu) {
        return res.status(404).json({ error: 'Main menu not found' });
      }

      const menuData = JSON.parse(mainMenu.menuJson);
      
      res.json({
        sessionId: session.sessionId,
        response: menuData.welcome || 'Welcome to IBT B2B Connect',
        menu: menuData.options || []
      });

    } catch (error) {
      console.error('USSD start error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // USSD response handler
  async ussdRespond(req: Request, res: Response) {
    try {
      const { sessionId, userInput } = req.body;

      if (!sessionId || !userInput) {
        return res.status(400).json({ error: 'Session ID and user input are required' });
      }

      // Find session
      const session = await prisma.uSSDSession.findUnique({
        where: { sessionId }
      });

      if (!session) {
        return res.status(404).json({ error: 'Session not found' });
      }

      // Parse collected data
      const collectedData = JSON.parse(session.collectedData || '{}');

      // Handle different menu states
      let response = '';
      let newMenu = session.currentMenu;

      switch (session.currentMenu) {
        case 'MAIN':
          if (userInput === '1') {
            newMenu = 'LOGIN';
            response = 'Enter your email:';
          } else if (userInput === '2') {
            newMenu = 'REGISTER';
            response = 'Enter your email:';
          } else if (userInput === '3') {
            newMenu = 'SEARCH';
            response = 'Enter search term:';
          } else {
            response = 'Invalid option. Please try again:';
          }
          break;

        case 'LOGIN':
          collectedData.email = userInput;
          newMenu = 'LOGIN_PASSWORD';
          response = 'Enter your password:';
          break;

        case 'LOGIN_PASSWORD':
          collectedData.password = userInput;
          // Attempt login
          try {
            const user = await prisma.user.findUnique({
              where: { email: collectedData.email }
            });

            if (user && await bcrypt.compare(userInput, user.passwordHash)) {
              response = 'Login successful! Welcome back.';
              newMenu = 'MAIN';
            } else {
              response = 'Invalid credentials. Try again:';
              newMenu = 'LOGIN';
            }
          } catch (error) {
            response = 'Login failed. Try again:';
            newMenu = 'LOGIN';
          }
          break;

        default:
          response = 'Service unavailable. Please try again later.';
          newMenu = 'MAIN';
      }

      // Update session
      await prisma.uSSDSession.update({
        where: { sessionId },
        data: {
          currentMenu: newMenu,
          collectedData: JSON.stringify(collectedData)
        }
      });

      res.json({
        sessionId,
        response,
        menu: newMenu === 'MAIN' ? ['1. Login', '2. Register', '3. Search'] : []
      });

    } catch (error) {
      console.error('USSD respond error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};
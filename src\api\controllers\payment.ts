import { Request, Response } from 'express';
import QRCode from 'qrcode';
import { prisma } from '../app';

export const paymentController = {
  // Create QR code for payment
  async createQRCode(req: Request, res: Response) {
    try {
      const { orderId, amount } = req.body;
      const userId = req.user!.userId;

      if (!orderId || !amount) {
        return res.status(400).json({ error: 'Order ID and amount are required' });
      }

      // Get order and verify ownership
      const order = await prisma.order.findUnique({
        where: { id: orderId }
      });

      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      if (order.userId !== userId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      if (order.status !== 'PENDING') {
        return res.status(400).json({ error: 'Order is not in pending status' });
      }

      // Generate QR code data
      const qrData = JSON.stringify({
        orderId,
        amount,
        timestamp: new Date().toISOString(),
        paymentMethod: 'QR'
      });

      // Generate QR code as base64
      const qrCodeBase64 = await QRCode.toDataURL(qrData, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      // Update order with QR code URL
      await prisma.order.update({
        where: { id: orderId },
        data: {
          qrCodeUrl: qrCodeBase64
        }
      });

      res.json({
        message: 'QR code generated successfully',
        qrCodeUrl: qrCodeBase64,
        orderId,
        amount
      });

    } catch (error) {
      console.error('Create QR code error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Process DCash payment (stub implementation)
  async processDCashPayment(req: Request, res: Response) {
    try {
      const { orderId, dcashTransactionId } = req.body;
      const userId = req.user!.userId;

      if (!orderId || !dcashTransactionId) {
        return res.status(400).json({ error: 'Order ID and DCash transaction ID are required' });
      }

      // Get order and verify ownership
      const order = await prisma.order.findUnique({
        where: { id: orderId }
      });

      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      if (order.userId !== userId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      if (order.status !== 'PENDING') {
        return res.status(400).json({ error: 'Order is not in pending status' });
      }

      // Stub: Simulate DCash payment processing
      // In a real implementation, this would integrate with DCash 2.0 SDK
      const paymentSuccess = true; // Simulate successful payment

      if (!paymentSuccess) {
        return res.status(400).json({ error: 'DCash payment failed' });
      }

      // Update order status and add transaction ID
      const updatedOrder = await prisma.order.update({
        where: { id: orderId },
        data: {
          status: 'PAID',
          dcashTransactionId
        },
        include: {
          vendor: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  name: true
                }
              }
            }
          },
          items: {
            include: {
              product: {
                include: {
                  category: true
                }
              }
            }
          }
        }
      });

      // Log security event
      await prisma.securityLog.create({
        data: {
          userId,
          eventType: 'PAYMENT',
          status: 'SUCCESS'
        }
      });

      // Log analytics event
      await prisma.analyticsEvent.create({
        data: {
          entityId: userId,
          type: 'VENDOR_SALE',
          data: JSON.stringify({
            orderId,
            paymentMethod: 'DCASH',
            amount: order.total,
            transactionId: dcashTransactionId
          })
        }
      });

      res.json({
        message: 'DCash payment processed successfully',
        order: updatedOrder
      });

    } catch (error) {
      console.error('Process DCash payment error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};
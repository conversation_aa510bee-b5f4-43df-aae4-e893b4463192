import express from 'express';
import { authenticateToken, authorizeRole } from '../middleware/validation';
import { vendorController } from '../controllers/vendor';

const router = express.Router();

// Public routes
router.get('/', vendorController.getAllVendors);
router.get('/:id', vendorController.getVendorById);

// Protected routes
router.post('/', authenticateToken, authorizeRole(['USER', 'ADMIN']), vendorController.createVendor);
router.patch('/:id', authenticateToken, authorizeRole(['VENDOR', 'ADMIN']), vendorController.updateVendor);

export default router;
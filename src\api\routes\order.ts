import express from 'express';
import { authenticateToken } from '../middleware/validation';
import { orderController } from '../controllers/order';

const router = express.Router();

// Protected routes
router.get('/', authenticateToken, orderController.getUserOrders);
router.post('/', authenticateToken, orderController.createOrder);
router.get('/:id', authenticateToken, orderController.getOrderById);

export default router;
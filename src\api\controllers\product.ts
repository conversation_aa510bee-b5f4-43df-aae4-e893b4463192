import { Request, Response } from 'express';
import { prisma } from '../app';

export const productController = {
  // Get all products with search and filters
  async getAllProducts(req: Request, res: Response) {
    try {
      const { 
        category, 
        lat, 
        lng, 
        radius = 10, 
        page = 1, 
        limit = 20,
        vendorType,
        minPrice,
        maxPrice,
        search
      } = req.query;

      const where: any = {};

      // Category filter
      if (category) {
        where.category = {
          slug: category
        };
      }

      // Vendor type filter
      if (vendorType) {
        where.vendor = {
          vendorType: vendorType
        };
      }

      // Price range filter
      if (minPrice || maxPrice) {
        where.price = {};
        if (minPrice) where.price.gte = parseFloat(minPrice as string);
        if (maxPrice) where.price.lte = parseFloat(maxPrice as string);
      }

      // Search filter
      if (search) {
        where.OR = [
          { title: { contains: search as string, mode: 'insensitive' } },
          { description: { contains: search as string, mode: 'insensitive' } }
        ];
      }

      // Location filter (if lat and lng provided)
      if (lat && lng) {
        const userLat = parseFloat(lat as string);
        const userLng = parseFloat(lng as string);
        const radiusKm = parseFloat(radius as string);

        // Get products within radius
        const products = await prisma.product.findMany({
          where: {
            ...where,
            AND: [
              {
                OR: [
                  // Products with location
                  {
                    latitude: { not: null },
                    longitude: { not: null },
                    vendor: {
                      latitude: { not: null },
                      longitude: { not: null }
                    }
                  },
                  // Products without location (show all)
                  {
                    latitude: null,
                    longitude: null
                  }
                ]
              }
            ]
          },
          include: {
            vendor: {
              select: {
                id: true,
                businessName: true,
                vendorType: true,
                latitude: true,
                longitude: true
              }
            },
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
                type: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        });

        // Filter by distance for products with location
        const filteredProducts = products.filter(product => {
          if (!product.latitude || !product.longitude || !product.vendor.latitude || !product.vendor.longitude) {
            return true; // Include products without location
          }

          const distance = calculateDistance(
            userLat, userLng,
            product.latitude || product.vendor.latitude!,
            product.longitude || product.vendor.longitude!
          );

          return distance <= radiusKm;
        });

        // Add distance to products
        const productsWithDistance = filteredProducts.map(product => {
          let distance = null;
          if (product.latitude && product.longitude && product.vendor.latitude && product.vendor.longitude) {
            distance = calculateDistance(
              userLat, userLng,
              product.latitude || product.vendor.latitude!,
              product.longitude || product.vendor.longitude!
            );
          }
          return { ...product, distance };
        });

        // Pagination
        const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
        const paginatedProducts = productsWithDistance.slice(skip, skip + parseInt(limit as string));

        res.json({
          products: paginatedProducts,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total: filteredProducts.length,
            totalPages: Math.ceil(filteredProducts.length / parseInt(limit as string))
          }
        });

      } else {
        // No location filter - regular pagination
        const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
        
        const [products, total] = await Promise.all([
          prisma.product.findMany({
            where,
            include: {
              vendor: {
                select: {
                  id: true,
                  businessName: true,
                  vendorType: true,
                  latitude: true,
                  longitude: true
                }
              },
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  type: true
                }
              }
            },
            orderBy: {
              createdAt: 'desc'
            },
            skip,
            take: parseInt(limit as string)
          }),
          prisma.product.count({ where })
        ]);

        res.json({
          products,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            totalPages: Math.ceil(total / parseInt(limit as string))
          }
        });
      }

    } catch (error) {
      console.error('Get products error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Get product by ID
  async getProductById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const product = await prisma.product.findUnique({
        where: { id },
        include: {
          vendor: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  name: true
                }
              }
            }
          },
          category: true
        }
      });

      if (!product) {
        return res.status(404).json({ error: 'Product not found' });
      }

      res.json(product);
    } catch (error) {
      console.error('Get product error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Create product
  async createProduct(req: Request, res: Response) {
    try {
      const { title, description, price, categoryId, images, latitude, longitude, stock } = req.body;
      const userId = req.user!.userId;

      // Get vendor for this user
      const vendor = await prisma.vendor.findUnique({
        where: { userId }
      });

      if (!vendor) {
        return res.status(404).json({ error: 'Vendor not found' });
      }

      // Validate category
      const category = await prisma.category.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return res.status(404).json({ error: 'Category not found' });
      }

      // Create product
      const product = await prisma.product.create({
        data: {
          vendorId: vendor.id,
          categoryId,
          title,
          description,
          price: parseFloat(price),
          images: images ? JSON.stringify(images) : null,
          latitude: latitude || null,
          longitude: longitude || null,
          stock: parseInt(stock) || 0
        },
        include: {
          vendor: {
            select: {
              id: true,
              businessName: true,
              vendorType: true
            }
          },
          category: true
        }
      });

      res.status(201).json({
        message: 'Product created successfully',
        product
      });

    } catch (error) {
      console.error('Create product error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Update product
  async updateProduct(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { title, description, price, categoryId, images, latitude, longitude, stock } = req.body;
      const userId = req.user!.userId;

      // Find product and verify ownership
      const product = await prisma.product.findUnique({
        where: { id },
        include: {
          vendor: true
        }
      });

      if (!product) {
        return res.status(404).json({ error: 'Product not found' });
      }

      // Check if user is product owner or admin
      if (product.vendor.userId !== userId && req.user!.role !== 'ADMIN') {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Update product
      const updatedProduct = await prisma.product.update({
        where: { id },
        data: {
          ...(title && { title }),
          ...(description !== undefined && { description }),
          ...(price !== undefined && { price: parseFloat(price) }),
          ...(categoryId && { categoryId }),
          ...(images !== undefined && { images: images ? JSON.stringify(images) : null }),
          ...(latitude !== undefined && { latitude }),
          ...(longitude !== undefined && { longitude }),
          ...(stock !== undefined && { stock: parseInt(stock) })
        },
        include: {
          vendor: {
            select: {
              id: true,
              businessName: true,
              vendorType: true
            }
          },
          category: true
        }
      });

      res.json({
        message: 'Product updated successfully',
        product: updatedProduct
      });

    } catch (error) {
      console.error('Update product error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Delete product
  async deleteProduct(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user!.userId;

      // Find product and verify ownership
      const product = await prisma.product.findUnique({
        where: { id },
        include: {
          vendor: true
        }
      });

      if (!product) {
        return res.status(404).json({ error: 'Product not found' });
      }

      // Check if user is product owner or admin
      if (product.vendor.userId !== userId && req.user!.role !== 'ADMIN') {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Delete product
      await prisma.product.delete({
        where: { id }
      });

      res.json({
        message: 'Product deleted successfully'
      });

    } catch (error) {
      console.error('Delete product error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};

// Helper function to calculate distance between two points
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
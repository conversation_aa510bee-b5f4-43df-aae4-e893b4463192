# IBT B2B Connect Marketplace MVP

A comprehensive B2B marketplace platform built with Next.js, Express, Prisma, and PostgreSQL. This MVP provides a foundation for connecting local businesses with customers through an intuitive web and mobile interface.

## 🚀 Features

### Core Functionality
- **User Authentication & Authorization**: JWT-based authentication with role-based access control
- **Vendor Management**: Complete vendor dashboard for product and order management
- **Product Catalog**: Advanced product search with filtering and location-based results
- **Shopping Cart & Checkout**: Seamless shopping experience with multiple payment methods
- **Order Management**: Real-time order tracking and status updates
- **Social Feed**: Vendor social media integration for customer engagement
- **USSD Support**: Mobile-friendly interface for users without internet access
- **Analytics**: Comprehensive analytics for vendors and administrators

### Technical Features
- **Geolocation Services**: Location-based product discovery and vendor search
- **Real-time Communication**: WebSocket integration for live updates
- **Multi-payment Support**: QR codes, DCash, mobile money, and traditional payments
- **Responsive Design**: Mobile-first approach with progressive enhancement
- **Workflow Automation**: n8n integration for automated business processes
- **Security**: Comprehensive security logging and monitoring

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript 5
- **Styling**: Tailwind CSS 4 with shadcn/ui components
- **Maps**: Leaflet with React Leaflet
- **State Management**: Zustand and TanStack Query
- **Authentication**: NextAuth.js v4

### Backend
- **Runtime**: Node.js with Express.js
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT tokens
- **Real-time**: Socket.IO for WebSocket support
- **File Storage**: AWS S3 (configured for images and assets)

### Integration & Automation
- **Workflow Automation**: n8n for business process automation
- **Communication**: SendGrid for emails, Twilio for SMS
- **Payment Integration**: DCash 2.0 SDK (stub implementation)
- **Analytics**: Custom analytics platform integration

### Infrastructure
- **Security**: AWS WAF, RDS Multi-AZ (placeholders)
- **Mobile**: USSD support with Twilio/Africa's Talking (stub)
- **Monitoring**: Comprehensive logging and error tracking

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- PostgreSQL 14+
- n8n (for workflow automation)
- AWS Account (for S3 and other services)

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ibt-b2b-connect-marketplace
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**
   ```bash
   # Create PostgreSQL database
   createdb ibt_b2b_connect
   
   # Run migrations
   npm run db:push
   
   # Generate Prisma client
   npm run db:generate
   ```

5. **Seed initial data**
   ```bash
   npx tsx src/api/seed.ts
   ```

6. **Start the development server**
   ```bash
   npm run dev
   ```

The application will be available at `http://localhost:3000`.

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/ibt_b2b_connect"

# Authentication
JWT_SECRET="your-super-secret-jwt-key-here"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-here"

# API Configuration
API_URL="http://localhost:3000/api"
FRONTEND_URL="http://localhost:3000"

# AWS Configuration
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="your-aws-region"
AWS_S3_BUCKET="your-s3-bucket-name"

# Email Configuration (SendGrid)
SENDGRID_API_KEY="your-sendgrid-api-key"
SENDGRID_FROM_EMAIL="<EMAIL>"

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"

# Payment Configuration
DCASH_API_URL="https://api.dcash.example.com"
DCASH_API_KEY="your-dcash-api-key"

# USSD Configuration
USSD_SERVICE_URL="https://ussd-service.example.com"
AFRICAS_TALKING_API_KEY="your-africas-talking-api-key"

# n8n Configuration
N8N_WEBHOOK_URL="http://localhost:5678"
N8N_API_KEY="your-n8n-api-key"

# Analytics
ANALYTICS_API_URL="http://localhost:3001/api"
SOCIAL_MEDIA_API_URL="https://social-media-api.example.com"
TRENDING_API_URL="https://trending-api.example.com"

# Admin Configuration
ADMIN_URL="http://localhost:3001/admin"
ADMIN_API_URL="http://localhost:3001/api"
VENDOR_WEBHOOK_SECRET="your-vendor-webhook-secret"
```

### Database Configuration

The application uses PostgreSQL with the following schema structure:

- **Users**: Authentication and profile management
- **Vendors**: Business accounts with product management
- **Categories**: Product classification system
- **Products**: Marketplace inventory with location data
- **Orders**: Transaction management with payment tracking
- **SocialFeed**: Vendor social media integration
- **USSD Sessions**: Mobile interface sessions
- **Analytics**: Event tracking and business intelligence

### n8n Workflow Configuration

Import the provided n8n workflow templates:

1. **Vendor Signup** (`n8n-workflows/vendor-signup.json`)
   - Triggers: HTTP webhook on vendor registration
   - Actions: SendGrid email, Slack alert, admin task creation

2. **Order Created** (`n8n-workflows/order-created.json`)
   - Triggers: Database webhook on order creation
   - Actions: Email/SMS notifications, vendor webhook, order status update

3. **Payment Succeeded** (`n8n-workflows/payment-succeeded.json`)
   - Triggers: QR/DCash payment confirmation
   - Actions: Order status update, notifications, analytics logging

4. **Social Feed Posted** (`n8n-workflows/social-feed-posted.json`)
   - Triggers: Vendor social media posts
   - Actions: Analytics, follower notifications, social media sharing

To import workflows:
1. Start n8n: `npx n8n start`
2. Access n8n at `http://localhost:5678`
3. Import JSON files from the `n8n-workflows` directory
4. Configure credentials for each service integration

## 🏗️ Project Structure

```
ibt-b2b-connect-marketplace/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── api/                # API routes
│   │   ├── auth/               # Authentication pages
│   │   ├── cart/               # Shopping cart
│   │   ├── checkout/           # Checkout process
│   │   ├── orders/             # Order management
│   │   ├── search/             # Product search
│   │   ├── signin/             # User sign in
│   │   ├── signup/             # User sign up
│   │   ├── ussd/               # USSD interface
│   │   ├── vendor/             # Vendor pages
│   │   └── globals.css          # Global styles
│   ├── components/            # React components
│   │   ├── ui/                 # shadcn/ui components
│   │   └── shared/             # Shared components
│   ├── hooks/                 # Custom React hooks
│   ├── lib/                   # Utility libraries
│   │   ├── db.ts              # Database client
│   │   ├── socket.ts          # Socket.IO setup
│   │   └── utils.ts           # Utility functions
│   └── api/                   # Express API
│       ├── app.ts              # Express app setup
│       ├── controllers/        # API controllers
│       ├── middleware/         # Express middleware
│       ├── routes/             # API routes
│       ├── services/           # Business logic
│       └── seed.ts             # Database seed script
├── prisma/
│   └── schema.prisma          # Database schema
├── n8n-workflows/             # n8n workflow templates
├── public/                    # Static assets
├── docs/                      # Documentation
├── package.json
├── tailwind.config.ts
├── tsconfig.json
└── README.md
```

## 🚀 Deployment

### Development Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start production server**
   ```bash
   npm start
   ```

### Production Deployment

#### Docker Deployment

```dockerfile
# Dockerfile example
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

#### Environment-specific Configurations

**Development:**
- Uses SQLite for local development
- Hot reload enabled
- Debug logging enabled

**Production:**
- PostgreSQL with connection pooling
- Optimized builds
- Production logging and monitoring

### Infrastructure Requirements

- **Database**: PostgreSQL 14+ with PostGIS extension
- **Redis**: For session management and caching (optional)
- **Object Storage**: AWS S3 or equivalent
- **Email Service**: SendGrid or equivalent
- **SMS Service**: Twilio or Africa's Talking
- **Workflow Automation**: n8n instance
- **Monitoring**: Application performance monitoring

## 🔐 Security Features

### Authentication & Authorization
- JWT-based authentication with refresh tokens
- Role-based access control (User, Vendor, Admin)
- Password hashing with bcrypt
- Session management with secure cookies

### Data Protection
- Input validation and sanitization
- SQL injection prevention with Prisma ORM
- XSS protection with React and Next.js
- CSRF protection with double-submit cookies

### API Security
- Rate limiting on API endpoints
- Request validation middleware
- Secure headers configuration
- CORS configuration for cross-origin requests

### Infrastructure Security
- AWS WAF integration points
- RDS Multi-AZ configuration
- SSL/TLS termination
- Regular security audits and logging

## 📱 Mobile Support

### USSD Interface

The application includes USSD support for users without internet access:

**Dial Code**: `*123#` (configurable)

**Available Services:**
- User authentication
- Product search
- Order management
- Account management

**Configuration:**
```env
USSD_SERVICE_URL="https://your-ussd-service.com"
USSD_SHORT_CODE="*123#"
```

### Responsive Design

- Mobile-first approach
- Progressive Web App (PWA) capabilities
- Touch-optimized interfaces
- Offline functionality support

## 💳 Payment Integration

### Supported Payment Methods

1. **QR Code Payments**
   - Dynamic QR code generation
   - Mobile scanning integration
   - Real-time payment confirmation

2. **DCash Integration**
   - DCash 2.0 SDK integration (stub implementation)
   - Transaction status tracking
   - Refund and cancellation support

3. **Mobile Money**
   - Multiple provider support
   - USSD-based payments
   - Confirmation SMS notifications

4. **Traditional Payments**
   - Credit/Debit card processing
   - Cash on delivery
   - Bank transfers

### Payment Flow

1. **Customer selects products** → **Adds to cart**
2. **Proceeds to checkout** → **Selects payment method**
3. **Completes payment** → **Order confirmation**
4. **Vendor notified** → **Order fulfillment begins**
5. **Status updates** → **Delivery completion**

## 📊 Analytics & Reporting

### Vendor Analytics
- Sales performance metrics
- Customer behavior analysis
- Product popularity tracking
- Revenue optimization insights

### Admin Analytics
- Platform-wide metrics
- User growth analytics
- Transaction volume tracking
- System performance monitoring

### Integration Points
- Custom analytics platform for IBT Solutions LLC
- Google Analytics integration
- Real-time dashboard updates
- Export capabilities for reporting

## 🔧 Development Workflow

### Code Standards

- **TypeScript**: Strict type checking enabled
- **ESLint**: Code quality and consistency
- **Prettier**: Code formatting
- **Husky**: Git hooks for pre-commit checks

### Testing Strategy

- **Unit Testing**: Jest for individual component testing
- **Integration Testing**: Supertest for API endpoints
- **E2E Testing**: Playwright for user journey testing
- **Database Testing**: Prisma test environment

### Git Workflow

1. **Feature Branches**: Create branches for new features
2. **Pull Requests**: Code review and approval process
3. **CI/CD**: Automated testing and deployment
4. **Version Control**: Semantic versioning

## 🐛 Troubleshooting

### Common Issues

**Database Connection Issues**
```bash
# Check database connectivity
npm run db:push

# Regenerate Prisma client
npm run db:generate
```

**Build Errors**
```bash
# Clear Next.js cache
rm -rf .next
npm run build
```

**Port Conflicts**
```bash
# Check port usage
lsof -i :3000

# Kill process on port
kill -9 <PID>
```

### Debug Mode

Enable debug logging:
```bash
DEBUG=ibt:* npm run dev
```

### Performance Optimization

- **Database**: Index optimization and query tuning
- **Frontend**: Code splitting and lazy loading
- **API**: Response caching and rate limiting
- **Assets**: Image optimization and CDN integration

## 🤝 Contributing

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Coding Standards

- Follow TypeScript best practices
- Use existing shadcn/ui components
- Maintain consistent code style
- Add appropriate documentation

### Pull Request Process

1. **Description**: Clear description of changes
2. **Testing**: Include test coverage
3. **Documentation**: Update relevant documentation
4. **Review**: Address review comments

## 📄 License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **IBT Solutions LLC**: For the opportunity to build this MVP
- **Open Source Community**: For the excellent tools and libraries
- **Contributors**: Everyone who has contributed to this project

## 📞 Support

For support and questions:

- **Documentation**: Check the `/docs` directory
- **Issues**: Create an issue on GitHub
- **Email**: <EMAIL>
- **Community**: Join our Discord server

---

**Note**: This is an MVP implementation. Some features are stubbed or placeholder implementations for demonstration purposes. Please refer to the specific documentation for integration points and extension guidance.
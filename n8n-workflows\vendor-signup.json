{"name": "Vendor Signup Workflow", "nodes": [{"parameters": {}, "id": "1", "name": "When clicking 'Test workflow'", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"httpMethod": "POST", "path": "vendor-signup", "options": {}}, "id": "2", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [460, 300], "webhookId": "vendor-signup-webhook"}, {"parameters": {"jsCode": "// Parse webhook data\nconst webhookData = $node[\"Webhook\"].json[\"body\"];\n\n// Extract vendor information\nconst vendorInfo = {\n  vendorId: webhookData.vendorId,\n  businessName: webhookData.businessName,\n  vendorType: webhookData.vendorType,\n  email: webhookData.email,\n  name: webhookData.name,\n  timestamp: new Date().toISOString()\n};\n\n// Set up email content\nconst emailContent = {\n  to: webhookData.email,\n  subject: `Welcome to IBT B2B Connect, ${webhookData.businessName}!`,\n  html: `\n    <h2>Welcome to IBT B2B Connect!</h2>\n    <p>Dear ${webhookData.name},</p>\n    <p>Thank you for registering your business <strong>${webhookData.businessName}</strong> on IBT B2B Connect Marketplace.</p>\n    \n    <h3>What's Next?</h3>\n    <ul>\n      <li>Complete your vendor profile setup</li>\n      <li>Add your first products to the marketplace</li>\n      <li>Set up your payment preferences</li>\n      <li>Start receiving orders from customers</li>\n    </ul>\n    \n    <p>Your application is currently under review. You'll receive another email once your account is approved.</p>\n    \n    <p>If you have any questions, please don't hesitate to contact our support team.</p>\n    \n    <p>Best regards,<br>The IBT B2B Connect Team</p>\n  `,\n  text: `Welcome to IBT B2B Connect, ${webhookData.name}!\\n\\nThank you for registering your business ${webhookData.businessName} on IBT B2B Connect Marketplace.\\n\\nWhat's Next?\\n- Complete your vendor profile setup\\n- Add your first products to the marketplace\\n- Set up your payment preferences\\n- Start receiving orders from customers\\n\\nYour application is currently under review. You'll receive another email once your account is approved.\\n\\nIf you have any questions, please don't hesitate to contact our support team.\\n\\nBest regards,\\nThe IBT B2B Connect Team`\n};\n\n// Set up Slack notification\nconst slackMessage = {\n  text: `🎉 New Vendor Signup!`,\n  blocks: [\n    {\n      type: \"section\",\n      text: {\n        type: \"mrkdwn\",\n        text: `*New Vendor Registration Received*`\n      }\n    },\n    {\n      type: \"section\",\n      fields: [\n        {\n          type: \"mrkdwn\",\n          text: `*Business Name:*\\n${webhookData.businessName}`\n        },\n        {\n          type: \"mrkdwn\",\n          text: `*Vendor Type:*\\n${webhookData.vendorType}`\n        },\n        {\n          type: \"mrkdwn\",\n          text: `*Contact Person:*\\n${webhookData.name}`\n        },\n        {\n          type: \"mrkdwn\",\n          text: `*Email:*\\n${webhookData.email}`\n        },\n        {\n          type: \"mrkdwn\",\n          text: `*Signup Time:*\\n${new Date().toLocaleString()}`\n        }\n      ]\n    },\n    {\n      type: \"actions\",\n      elements: [\n        {\n          type: \"button\",\n          text: {\n            type: \"plain_text\",\n            text: \"Review Application\"\n          },\n          url: `${process.env.ADMIN_URL}/vendors/${webhookData.vendorId}`,\n          style: \"primary\"\n        },\n        {\n          type: \"button\",\n          text: {\n            type: \"plain_text\",\n            text: \"Contact Vendor\"\n          },\n          url: `mailto:${webhookData.email}`,\n          style: \"default\"\n        }\n      ]\n    }\n  ]\n};\n\n// Create admin task\nconst adminTask = {\n  title: `Review Vendor Application: ${webhookData.businessName}`,\n  description: `New vendor signup from ${webhookData.businessName} (${webhookData.vendorType})`,\n  assignee: \"admin-team\",\n  priority: \"medium\",\n  dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now\n  metadata: {\n    vendorId: webhookData.vendorId,\n    businessName: webhookData.businessName,\n    vendorType: webhookData.vendorType,\n    email: webhookData.email,\n    signupDate: new Date().toISOString()\n  }\n};\n\n// Return all data for next nodes\nreturn {\n  vendorInfo,\n  emailContent,\n  slackMessage,\n  adminTask\n};"}, "id": "3", "name": "Prepare Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"resource": "email", "operation": "send", "fromEmail": "<EMAIL>", "toEmail": "={{ $node[\"Prepare Data\"].json[\"emailContent\"][\"to\"] }}", "subject": "={{ $node[\"Prepare Data\"].json[\"emailContent\"][\"subject\"] }}", "text": "={{ $node[\"Prepare Data\"].json[\"emailContent\"][\"text\"] }}", "html": "={{ $node[\"Prepare Data\"].json[\"emailContent\"][\"html\"] }}", "options": {}}, "id": "4", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [900, 200], "credentials": {"sendGridApi": {"id": "1", "name": "SendGrid account"}}}, {"parameters": {"channel": "#vendor-signups", "text": "={{ JSON.stringify($node[\"Prepare Data\"].json[\"slackMessage\"]) }}", "options": {}}, "id": "5", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [900, 400], "credentials": {"slackApi": {"id": "1", "name": "Slack account"}}}, {"parameters": {"url": "={{ process.env.ADMIN_API_URL }}/tasks", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $node[\"Prepare Data\"].json[\"adminTask\"][\"title\"] }}"}, {"name": "description", "value": "={{ $node[\"Prepare Data\"].json[\"adminTask\"][\"description\"] }}"}, {"name": "assignee", "value": "={{ $node[\"Prepare Data\"].json[\"adminTask\"][\"assignee\"] }}"}, {"name": "priority", "value": "={{ $node[\"Prepare Data\"].json[\"adminTask\"][\"priority\"] }}"}, {"name": "dueDate", "value": "={{ $node[\"Prepare Data\"].json[\"adminTask\"][\"dueDate\"] }}"}, {"name": "metadata", "value": "={{ JSON.stringify($node[\"Prepare Data\"].json[\"adminTask\"][\"metadata\"]) }}"}]}, "options": {}}, "id": "6", "name": "Create Admin Task", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 600], "credentials": {"httpBasicAuth": {"id": "1", "name": "Admin API"}}}], "pinData": {}, "connections": {"When clicking 'Test workflow'": {"main": [[{"node": "Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Prepare Data", "type": "main", "index": 0}]]}, "Prepare Data": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}, {"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Create Admin Task", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "vendor-signup-workflow", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "vendor-signup", "name": "vendor-signup"}], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "createdAt": "2024-01-01T00:00:00.000Z"}
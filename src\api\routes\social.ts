import express from 'express';
import { authenticateToken, authorizeRole } from '../middleware/validation';
import { socialController } from '../controllers/social';

const router = express.Router();

// Public routes
router.get('/feed', socialController.getSocialFeed);
router.get('/feed/:vendorId', socialController.getVendorFeed);

// Protected routes
router.post('/feed', authenticateToken, authorizeRole(['VENDOR', 'ADMIN']), socialController.createSocialPost);

export default router;
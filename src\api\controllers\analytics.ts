import { Request, Response } from 'express';
import { prisma } from '../app';

export const analyticsController = {
  // Get vendor analytics (placeholder implementation for IBT Solutions LLC)
  async getVendorAnalytics(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user!.userId;

      // Get vendor and verify ownership
      const vendor = await prisma.vendor.findUnique({
        where: { id }
      });

      if (!vendor) {
        return res.status(404).json({ error: 'Vendor not found' });
      }

      // Check if user is vendor owner or admin
      if (vendor.userId !== userId && req.user!.role !== 'ADMIN') {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Get basic analytics data
      const [totalOrders, totalRevenue, productCount, socialPosts] = await Promise.all([
        prisma.order.count({
          where: { vendorId: id }
        }),
        prisma.order.aggregate({
          where: { 
            vendorId: id,
            status: 'PAID'
          },
          _sum: {
            total: true
          }
        }),
        prisma.product.count({
          where: { vendorId: id }
        }),
        prisma.socialFeed.count({
          where: { vendorId: id }
        })
      ]);

      // Get recent orders
      const recentOrders = await prisma.order.findMany({
        where: { vendorId: id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  title: true,
                  price: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      });

      // Get top products by sales
      const topProducts = await prisma.orderItem.groupBy({
        by: ['productId'],
        where: {
          order: {
            vendorId: id
          }
        },
        _sum: {
          qty: true
        },
        _count: {
          productId: true
        },
        orderBy: {
          _sum: {
            qty: 'desc'
          }
        },
        take: 5
      });

      // Get product details for top products
      const topProductDetails = await Promise.all(
        topProducts.map(async (item) => {
          const product = await prisma.product.findUnique({
            where: { id: item.productId },
            select: {
              id: true,
              title: true,
              price: true,
              category: {
                select: {
                  name: true
                }
              }
            }
          });
          return {
            ...product,
            totalSold: item._sum.qty,
            orderCount: item._count.productId
          };
        })
      );

      res.json({
        vendor: {
          id: vendor.id,
          businessName: vendor.businessName,
          vendorType: vendor.vendorType,
          status: vendor.status
        },
        analytics: {
          totalOrders,
          totalRevenue: totalRevenue._sum.total || 0,
          productCount,
          socialPosts,
          averageOrderValue: totalOrders > 0 ? (totalRevenue._sum.total || 0) / totalOrders : 0
        },
        recentOrders,
        topProducts: topProductDetails,
        // Placeholder for additional analytics data
        placeholder: {
          message: 'Additional analytics data would be integrated with IBT Solutions LLC analytics platform',
          features: [
            'Customer demographics',
            'Sales trends and forecasting',
            'Inventory optimization',
            'Customer retention metrics',
            'Marketing campaign effectiveness'
          ]
        }
      });

    } catch (error) {
      console.error('Get vendor analytics error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};
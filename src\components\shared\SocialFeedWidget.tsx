'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  MessageSquare, 
  Heart, 
  Share2, 
  MoreHorizontal,
  TrendingUp,
  Calendar,
  Image as ImageIcon,
  Plus,
  Filter
} from 'lucide-react';

interface SocialPost {
  id: string;
  type: 'OFFER' | 'NEWS' | 'UPDATE' | 'AD';
  content: string;
  images?: string[];
  timestamp: string;
  vendor: {
    id: string;
    businessName: string;
    vendorType: string;
    user?: {
      name: string;
      email: string;
    };
  };
  analytics?: {
    views: number;
    likes: number;
    shares: number;
  };
}

interface SocialFeedWidgetProps {
  vendorId?: string;
  limit?: number;
  showHeader?: boolean;
  showCreateButton?: boolean;
  className?: string;
  onPostClick?: (post: SocialPost) => void;
  onCreatePost?: () => void;
}

export default function SocialFeedWidget({
  vendorId,
  limit = 5,
  showHeader = true,
  showCreateButton = false,
  className = '',
  onPostClick,
  onCreatePost
}: SocialFeedWidgetProps) {
  const [posts, setPosts] = useState<SocialPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'OFFER' | 'NEWS' | 'UPDATE' | 'AD'>('all');

  useEffect(() => {
    fetchPosts();
  }, [vendorId, limit, filter]);

  const fetchPosts = async () => {
    try {
      setLoading(true);
      let url = '/api/social/feed';
      
      if (vendorId) {
        url = `/api/social/feed/${vendorId}`;
      }

      const params = new URLSearchParams({
        limit: limit.toString()
      });

      if (filter !== 'all') {
        params.append('type', filter);
      }

      const response = await fetch(`${url}?${params}`);
      const data = await response.json();
      setPosts(data.posts || []);
    } catch (error) {
      console.error('Error fetching social feed:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'OFFER': return 'bg-green-100 text-green-800';
      case 'NEWS': return 'bg-blue-100 text-blue-800';
      case 'UPDATE': return 'bg-purple-100 text-purple-800';
      case 'AD': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'OFFER': return <TrendingUp className="h-4 w-4" />;
      case 'NEWS': return <MessageSquare className="h-4 w-4" />;
      case 'UPDATE': return <Calendar className="h-4 w-4" />;
      case 'AD': return <Share2 className="h-4 w-4" />;
      default: return <MessageSquare className="h-4 w-4" />;
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const postTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - postTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return postTime.toLocaleDateString();
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="space-y-4">
            {[...Array(limit)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="bg-gray-200 rounded-full h-10 w-10"></div>
                  <div className="flex-1">
                    <div className="bg-gray-200 h-4 w-1/3 rounded mb-2"></div>
                    <div className="bg-gray-200 h-3 w-1/4 rounded"></div>
                  </div>
                </div>
                <div className="bg-gray-200 h-20 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center">
              <MessageSquare className="h-5 w-5 mr-2" />
              Social Feed
            </CardTitle>
            <div className="flex items-center gap-2">
              {showCreateButton && (
                <Button size="sm" onClick={onCreatePost}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Post
                </Button>
              )}
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
          
          {/* Filter Tabs */}
          <div className="flex gap-2 mt-4">
            {(['all', 'OFFER', 'NEWS', 'UPDATE', 'AD'] as const).map((type) => (
              <Button
                key={type}
                variant={filter === type ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter(type)}
                className="text-xs"
              >
                {type === 'all' ? 'All' : type}
              </Button>
            ))}
          </div>
        </CardHeader>
      )}

      <CardContent>
        {posts.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No posts found</p>
            {showCreateButton && (
              <Button className="mt-4" onClick={onCreatePost}>
                <Plus className="h-4 w-4 mr-2" />
                Create First Post
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {posts.map((post) => (
              <div
                key={post.id}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => onPostClick && onPostClick(post)}
              >
                {/* Vendor Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={`/api/vendor/${post.vendor.id}/avatar`} />
                      <AvatarFallback>
                        {post.vendor.businessName.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {post.vendor.businessName}
                      </h3>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Badge className={`text-xs ${getTypeColor(post.vendor.vendorType)}`}>
                          {post.vendor.vendorType}
                        </Badge>
                        <span>•</span>
                        <span>{formatTimeAgo(post.timestamp)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={`text-xs ${getTypeColor(post.type)}`}>
                      <div className="flex items-center gap-1">
                        {getTypeIcon(post.type)}
                        {post.type}
                      </div>
                    </Badge>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Post Content */}
                <div className="mb-3">
                  <p className="text-gray-900 whitespace-pre-wrap">
                    {post.content}
                  </p>
                </div>

                {/* Post Images */}
                {post.images && post.images.length > 0 && (
                  <div className="mb-3">
                    <div className={`grid gap-2 ${
                      post.images.length === 1 ? 'grid-cols-1' :
                      post.images.length === 2 ? 'grid-cols-2' :
                      'grid-cols-2 md:grid-cols-3'
                    }`}>
                      {post.images.slice(0, 6).map((image, index) => (
                        <div
                          key={index}
                          className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer"
                        >
                          <img
                            src={image}
                            alt={`Post image ${index + 1}`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src = 'https://via.placeholder.com/300x300';
                            }}
                          />
                          {post.images.length > 6 && index === 5 && (
                            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                              <span className="text-white font-medium">
                                +{post.images.length - 6} more
                              </span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Engagement */}
                <div className="flex items-center justify-between pt-3 border-t">
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1 hover:text-red-600 cursor-pointer">
                      <Heart className="h-4 w-4" />
                      {post.analytics?.likes || 0}
                    </div>
                    <div className="flex items-center gap-1 hover:text-blue-600 cursor-pointer">
                      <MessageSquare className="h-4 w-4" />
                      Comment
                    </div>
                    <div className="flex items-center gap-1 hover:text-green-600 cursor-pointer">
                      <Share2 className="h-4 w-4" />
                      Share
                    </div>
                  </div>
                  <div className="text-xs text-gray-500">
                    {post.analytics?.views || 0} views
                  </div>
                </div>
              </div>
            ))}

            {/* Load More */}
            {posts.length === limit && (
              <div className="text-center pt-4 border-t">
                <Button variant="outline" onClick={() => setLimit(prev => prev + 5)}>
                  Load More Posts
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
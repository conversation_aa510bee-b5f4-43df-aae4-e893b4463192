'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Trash2, 
  ArrowRight,
  Store,
  MapPin,
  CreditCard,
  Smartphone
} from 'lucide-react';

interface CartItem {
  id: string;
  qty: number;
  totalPrice: number;
  product: {
    id: string;
    title: string;
    price: number;
    images?: string;
    stock: number;
    vendor: {
      id: string;
      businessName: string;
      vendorType: string;
      latitude?: number;
      longitude?: number;
    };
    category: {
      name: string;
      type: string;
    };
  };
}

interface Cart {
  id: string;
  items: CartItem[];
  subtotal: number;
}

export default function CartPage() {
  const router = useRouter();
  const [cart, setCart] = useState<Cart | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState<string | null>(null);
  const [error, setError] = useState('');
  const [checkoutLoading, setCheckoutLoading] = useState(false);

  useEffect(() => {
    fetchCart();
  }, []);

  const fetchCart = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        router.push('/signin');
        return;
      }

      const response = await fetch('/api/cart', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCart(data.cart);
      } else if (response.status === 401) {
        router.push('/signin');
      } else {
        setError('Error loading cart');
      }
    } catch (error) {
      console.error('Error fetching cart:', error);
      setError('Error loading cart');
    } finally {
      setLoading(false);
    }
  };

  const updateQuantity = async (itemId: string, newQty: number) => {
    if (newQty < 1) return;

    setUpdating(itemId);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/cart', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          productId: itemId,
          qty: newQty
        })
      });

      if (response.ok) {
        const data = await response.json();
        setCart(data.cart);
      }
    } catch (error) {
      console.error('Error updating quantity:', error);
    } finally {
      setUpdating(null);
    }
  };

  const removeItem = async (itemId: string) => {
    if (!confirm('Remove this item from cart?')) return;

    setUpdating(itemId);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/cart', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          productId: itemId,
          qty: 0
        })
      });

      if (response.ok) {
        const data = await response.json();
        setCart(data.cart);
      }
    } catch (error) {
      console.error('Error removing item:', error);
    } finally {
      setUpdating(null);
    }
  };

  const clearCart = async () => {
    if (!confirm('Clear all items from cart?')) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/cart', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setCart(null);
      }
    } catch (error) {
      console.error('Error clearing cart:', error);
    }
  };

  const proceedToCheckout = () => {
    if (!cart || cart.items.length === 0) return;
    
    // Check if all items are from the same vendor
    const vendorIds = [...new Set(cart.items.map(item => item.product.vendor.id))];
    if (vendorIds.length > 1) {
      setError('All items must be from the same vendor to checkout');
      return;
    }

    router.push('/checkout');
  };

  const getCategoryColor = (type: string) => {
    switch (type) {
      case 'FOOD': return 'bg-green-100 text-green-800';
      case 'ECOMMERCE': return 'bg-blue-100 text-blue-800';
      case 'SERVICE': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <div className="flex items-center">
              <ShoppingCart className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Shopping Cart</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!cart || cart.items.length === 0 ? (
          <div className="text-center py-16">
            <ShoppingCart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Your cart is empty</h2>
            <p className="text-gray-600 mb-6">Add some products to get started!</p>
            <Button onClick={() => router.push('/')}>
              Continue Shopping
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle>Cart Items ({cart.items.length})</CardTitle>
                    <Button variant="outline" size="sm" onClick={clearCart}>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear Cart
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {cart.items.map((item) => (
                      <div key={item.id} className="border rounded-lg p-4">
                        <div className="flex gap-4">
                          {/* Product Image */}
                          <div className="w-20 h-20 bg-gray-200 rounded-lg flex-shrink-0">
                            {item.product.images && item.product.images.length > 0 ? (
                              <img
                                src={item.product.images[0]}
                                alt={item.product.title}
                                className="w-full h-full object-cover rounded-lg"
                                onError={(e) => {
                                  e.currentTarget.src = 'https://via.placeholder.com/80x80';
                                }}
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <ShoppingCart className="h-8 w-8 text-gray-400" />
                              </div>
                            )}
                          </div>

                          {/* Product Details */}
                          <div className="flex-1">
                            <div className="flex justify-between items-start mb-2">
                              <div>
                                <h3 className="font-medium text-gray-900">{item.product.title}</h3>
                                <div className="flex items-center gap-2 mt-1">
                                  <Badge className={`text-xs ${getCategoryColor(item.product.category.type)}`}>
                                    {item.product.category.type}
                                  </Badge>
                                  <Badge variant="outline" className="text-xs">
                                    {item.product.category.name}
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-2 mt-1 text-sm text-gray-600">
                                  <Store className="h-3 w-3" />
                                  {item.product.vendor.businessName}
                                </div>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeItem(item.product.id)}
                                disabled={updating === item.product.id}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>

                            <div className="flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => updateQuantity(item.product.id, item.qty - 1)}
                                  disabled={item.qty <= 1 || updating === item.product.id}
                                >
                                  <Minus className="h-3 w-3" />
                                </Button>
                                <Input
                                  type="number"
                                  min="1"
                                  max={item.product.stock}
                                  value={item.qty}
                                  onChange={(e) => {
                                    const newQty = parseInt(e.target.value);
                                    if (newQty > 0 && newQty <= item.product.stock) {
                                      updateQuantity(item.product.id, newQty);
                                    }
                                  }}
                                  className="w-16 text-center"
                                />
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => updateQuantity(item.product.id, item.qty + 1)}
                                  disabled={item.qty >= item.product.stock || updating === item.product.id}
                                >
                                  <Plus className="h-3 w-3" />
                                </Button>
                                <span className="text-sm text-gray-600">
                                  of {item.product.stock} available
                                </span>
                              </div>
                              <div className="text-right">
                                <p className="font-medium text-gray-900">
                                  ${item.totalPrice.toFixed(2)}
                                </p>
                                <p className="text-sm text-gray-600">
                                  ${item.product.price.toFixed(2)} each
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Order Summary */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Order Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Vendor Info */}
                    <div className="border-b pb-4">
                      <h4 className="font-medium text-gray-900 mb-2">Vendor</h4>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Store className="h-4 w-4" />
                        {cart.items[0].product.vendor.businessName}
                      </div>
                      {cart.items[0].product.vendor.latitude && cart.items[0].product.vendor.longitude && (
                        <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                          <MapPin className="h-4 w-4" />
                          Local vendor
                        </div>
                      )}
                    </div>

                    {/* Price Breakdown */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Subtotal ({cart.items.length} items)</span>
                        <span>${cart.subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Delivery</span>
                        <span className="text-green-600">Free</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Tax</span>
                        <span>$0.00</span>
                      </div>
                      <div className="border-t pt-2">
                        <div className="flex justify-between font-medium">
                          <span>Total</span>
                          <span className="text-lg">${cart.subtotal.toFixed(2)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Payment Methods */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Accepted Payment Methods</h4>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <CreditCard className="h-4 w-4" />
                          Credit/Debit Card
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Smartphone className="h-4 w-4" />
                          Mobile Money
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <span className="w-4 h-4 bg-black rounded"></span>
                          QR Code Payment
                        </div>
                      </div>
                    </div>

                    {/* Checkout Button */}
                    <Button 
                      className="w-full" 
                      onClick={proceedToCheckout}
                      disabled={checkoutLoading}
                    >
                      {checkoutLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Processing...
                        </>
                      ) : (
                        <>
                          Proceed to Checkout
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </>
                      )}
                    </Button>

                    <Button 
                      variant="outline" 
                      className="w-full"
                      onClick={() => router.push('/')}
                    >
                      Continue Shopping
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}